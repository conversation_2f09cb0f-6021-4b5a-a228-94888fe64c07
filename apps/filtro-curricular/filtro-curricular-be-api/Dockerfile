# Use the official Python base image
FROM python:3.11-slim-bookworm

# Install needed libraries and cleanup in the same layer to reduce image size
RUN apt-get -y update && \
    apt-get -y install --no-install-recommends curl build-essential && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Set the working directory inside the container
WORKDIR /app

# Copy the requirements file to the working directory
COPY src/requirements.txt .

# Install the Python dependencies
RUN pip install --no-cache-dir --upgrade -r requirements.txt

# Create necessary directories
RUN mkdir -p /app/local-env /app/config

# Copy the application src to the working directory
COPY ./src/app /app

# Ensure proper Python module structure
RUN touch /app/utils/__init__.py

# Set Python environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# Expose the port on which the application will run
EXPOSE 5000

# Environment variables will be provided by Azure App Service via Key Vault references
# No default values set to ensure proper Key Vault integration

# Add healthcheck
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5000/status || exit 1

# Run the FastAPI application using uvicorn server
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "5000", "--reload"]