# Secrets

```shell
aws secretsmanager put-secret-value \
   --secret-id cognisearch/cgnsrch-chatbot-be-secrets \
   --secret-string '{
    "GPT_API_KEY": "********************************************************************************************************************************************************************",
    "OPENAI_TOKEN": "********************************************************",
    "ASSISTANT_ID_JURIDICO": "asst_BlsI7FjYzOowkqTyQdCScfun",
    "ASSISTANT_ID_CALIDAD": "asst_sMK3uTeEJneA6GLUz9STubsy",
    "AWS_ACCESS_KEY_ID": "********************",
    "AWS_SECRET_ACCESS_KEY": "fHLI0vRReVosGTlpLbWvMEYfe4NOU9777Y4vV7JQ",
    "AWS_REGION": "us-east-2",
    "BUCKET_NAME": "bucketelpino1",
    "BUCKET_OWNER_ID": "2b84697402c500358dbf2b9a7a28f4e1dab1a2b8dbdd05d56b767ef9026f8dba",
    "AWS_ACCOUNT_ID": "************"
    }'
```
jherrera-rgt@JherreraA15:~/dev/github/jherrera-rgt/ragtech/apps/infrastructure/azure/filtro-curricular/scripts$ az keyvault secret set --vault-name "filtrocu-kv-dev-xg52u0" --name "openai-api-key" --value "********************************************************************************************************************************************************************" --output table
Name            Value
--------------  --------------------------------------------------------------------------------------------------------------------------------------------------------------------
openai-api-key  ********************************************************************************************************************************************************************
jherrera-rgt@JherreraA15:~/dev/github/jherrera-rgt/ragtech/apps/infrastructure/azure/filtro-curricular/scripts$ az keyvault secret set --vault-name "filtrocu-kv-dev-xg52u0" --name "openai-token" --value "********************************************************" --output table
Name          Value
------------  --------------------------------------------------------
openai-token  ********************************************************
jherrera-rgt@JherreraA15:~/dev/github/jherrera-rgt/ragtech/apps/infrastructure/azure/filtro-curricular/scripts$ az keyvault secret set --vault-name "filtrocu-kv-dev-xg52u0" --name "assistant-id-juridico" --value "asst_BlsI7FjYzOowkqTyQdCScfun" --output table
Name                   Value
---------------------  -----------------------------
assistant-id-juridico  asst_BlsI7FjYzOowkqTyQdCScfun
jherrera-rgt@JherreraA15:~/dev/github/jherrera-rgt/ragtech/apps/infrastructure/azure/filtro-curricular/scripts$ az keyvault secret set --vault-name "filtrocu-kv-dev-xg52u0" --name "assistant-id-calidad" --value "asst_sMK3uTeEJneA6GLUz9STubsy" --output table
Name                  Value
--------------------  -----------------------------
assistant-id-calidad  asst_sMK3uTeEJneA6GLUz9STubsy