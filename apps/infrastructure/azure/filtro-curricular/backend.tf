# Terraform Backend Configuration for Azure Storage
# This file configures remote state storage in Azure Storage Account
# for enterprise-grade state management and team collaboration

terraform {
  backend "azurerm" {
    # Resource group containing the storage account
    resource_group_name = "filtro-curricular-terraform-state-rg"
    
    # Storage account name (must be globally unique)
    # Updated to match existing storage account
    storage_account_name = "filtrocurriculartfst"
    
    # Container name for storing state files
    container_name = "terraform-state"
    
    # State file name - unique per environment
    # This will be overridden by GitHub Actions for different environments
    key = "filtro-curricular-dev.tfstate"
    
    # Optional: Use managed identity for authentication in Azure environments
    # use_msi = true
    
    # Optional: Specify subscription ID if different from default
    # subscription_id = "your-subscription-id"
    
    # Optional: Enable state locking (requires Standard storage account)
    # use_azuread_auth = true
  }
}

# Note: For different environments, the key will be dynamically set:
# - dev: filtro-curricular-dev.tfstate
# - staging: filtro-curricular-staging.tfstate  
# - uat: filtro-curricular-uat.tfstate
# - prod: filtro-curricular-prod.tfstate
