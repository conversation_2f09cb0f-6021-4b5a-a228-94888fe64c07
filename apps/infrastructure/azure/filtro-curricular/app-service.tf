# App Service Plan
resource "azurerm_service_plan" "main" {
  name                = "${var.project_name}-plan-${var.environment}"
  resource_group_name = azurerm_resource_group.main.name
  location            = azurerm_resource_group.main.location
  os_type             = "Linux"
  sku_name            = var.app_service_plan_sku

  tags = var.tags
}

# Backend App Configuration Module
module "backend_app_config" {
  source = "./modules/app-config"

  environment   = var.environment
  app_name      = "backend"
  app_port      = var.backend_port
  key_vault_name = azurerm_key_vault.main.name

  app_insights_key               = azurerm_application_insights.main.instrumentation_key
  app_insights_connection_string = azurerm_application_insights.main.connection_string

  # Enable OpenAI secrets for backend
  enable_openai_secrets  = true
  enable_storage_secrets = true

  # Custom app settings specific to backend
  custom_app_settings = {
    # Docker settings
    DOCKER_REGISTRY_SERVER_URL      = "https://${azurerm_container_registry.main.login_server}"
    DOCKER_REGISTRY_SERVER_USERNAME = azurerm_container_registry.main.admin_username
    DOCKER_REGISTRY_SERVER_PASSWORD = azurerm_container_registry.main.admin_password

    # Storage Configuration
    BUCKET_NAME = azurerm_storage_container.uploads.name

    # API Configuration
    API_VERSION = "v1"
    MAX_FILE_SIZE_MB = "50"

    # Debug settings
    DEBUG_MODE = var.environment == "dev" ? "true" : "false"
    STARTUP_DEBUG = "true"
  }
}

# Backend App Service
resource "azurerm_linux_web_app" "backend" {
  name                = "${var.project_name}-be-${var.environment}"
  resource_group_name = azurerm_resource_group.main.name
  location            = azurerm_service_plan.main.location
  service_plan_id     = azurerm_service_plan.main.id

  site_config {
    always_on = true

    application_stack {
      docker_image_name = "${azurerm_container_registry.main.login_server}/filtro-curricular-be-api:${var.backend_image_tag}"
    }



    # Health check
    health_check_path = "/status"

    # CORS settings for frontend integration
    cors {
      allowed_origins = [
        "https://${var.project_name}-fe-${var.environment}.azurewebsites.net",
        var.environment == "dev" ? "http://localhost:4200" : null
      ]
      support_credentials = true
    }
  }

  # System-assigned managed identity
  identity {
    type = "SystemAssigned"
  }

  # Use the app settings from the module
  app_settings = module.backend_app_config.app_settings

  logs {
    detailed_error_messages = true
    failed_request_tracing  = true
    
    application_logs {
      file_system_level = "Information"
    }
    
    http_logs {
      file_system {
        retention_in_days = 7
        retention_in_mb   = 35
      }
    }
  }

  tags = merge(var.tags, {
    Component = "backend"
    Service   = "app-service"
  })
}

# Frontend App Configuration Module
module "frontend_app_config" {
  source = "./modules/app-config"

  environment   = var.environment
  app_name      = "frontend"
  app_port      = var.frontend_port
  key_vault_name = azurerm_key_vault.main.name

  app_insights_key               = azurerm_application_insights.main.instrumentation_key
  app_insights_connection_string = azurerm_application_insights.main.connection_string

  # Frontend typically doesn't need OpenAI secrets directly
  enable_openai_secrets  = false
  enable_storage_secrets = false

  # Custom app settings specific to frontend
  custom_app_settings = {
    # Docker settings
    DOCKER_REGISTRY_SERVER_URL      = "https://${azurerm_container_registry.main.login_server}"
    DOCKER_REGISTRY_SERVER_USERNAME = azurerm_container_registry.main.admin_username
    DOCKER_REGISTRY_SERVER_PASSWORD = azurerm_container_registry.main.admin_password

    # Backend API URL
    API_URL = "https://${azurerm_linux_web_app.backend.default_hostname}"

    # Frontend Configuration
    APP_TITLE = "Filtro Curricular"
    THEME = var.environment == "prod" ? "production" : "development"
  }
}

# Frontend App Service
resource "azurerm_linux_web_app" "frontend" {
  name                = "${var.project_name}-fe-${var.environment}"
  resource_group_name = azurerm_resource_group.main.name
  location            = azurerm_service_plan.main.location
  service_plan_id     = azurerm_service_plan.main.id

  site_config {
    always_on = true

    application_stack {
      docker_image_name = "${azurerm_container_registry.main.login_server}/filtro-curricular-fe-web:${var.frontend_image_tag}"
    }
  }

  # System-assigned managed identity
  identity {
    type = "SystemAssigned"
  }

  # Use the app settings from the module
  app_settings = module.frontend_app_config.app_settings

  logs {
    detailed_error_messages = true
    failed_request_tracing  = true

    application_logs {
      file_system_level = "Information"
    }

    http_logs {
      file_system {
        retention_in_days = 7
        retention_in_mb   = 35
      }
    }
  }

  tags = merge(var.tags, {
    Component = "frontend"
    Service   = "app-service"
  })
}
