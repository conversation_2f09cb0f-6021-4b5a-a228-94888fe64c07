# 🚨 IMMEDIATE ACTION PLAN - Fix HTTP 503 Errors

## 📊 Current Status (Verified)

✅ **Infrastructure**: Terraform deployed successfully  
✅ **Key Vault**: Exists and accessible  
✅ **App Services**: Running with managed identity  
❌ **Secrets**: Missing from Key Vault (causing HTTP 503)  
❌ **App Configuration**: No Key Vault references in app settings  

## 🔥 CRITICAL ISSUE IDENTIFIED

**Root Cause**: The Key Vault secrets are missing because:
1. GitHub environment secrets are not configured
2. Terraform deployed with placeholder values from `dev.tfvars`
3. App Services cannot access OpenAI credentials → HTTP 503 errors

## 🎯 IMMEDIATE SOLUTION (Choose One)

### Option A: Configure GitHub Secrets (Recommended for CI/CD)

1. **Configure GitHub Environment Secrets**:
   ```
   Go to: GitHub Repository → Settings → Environments → dev → Secrets
   
   Add these secrets:
   - OPENAI_API_KEY=sk-proj-[your-actual-key]
   - OPENAI_TOKEN=sk-[your-actual-token]
   - ASSISTANT_ID_JURIDICO=asst_[your-juridico-id]
   - ASSISTANT_ID_CALIDAD=asst_[your-calidad-id]
   ```

2. **Redeploy Infrastructure**:
   ```bash
   # Trigger GitHub Actions workflow
   gh workflow run filtro-curricular-infrastructure.yml --field environment=dev
   
   # Or manually with Terraform
   cd apps/infrastructure/azure/filtro-curricular
   terraform apply -var-file=environments/dev.tfvars
   ```

### Option B: Use Enhanced Secret Management Script (Quick Fix)

1. **Update Secrets Directly**:
   ```bash
   cd apps/infrastructure/azure/filtro-curricular/scripts
   ./manage-secrets.sh dev update
   ```

2. **Restart Applications**:
   ```bash
   # Restart backend
   az webapp restart --name filtro-curricular-be-dev --resource-group filtro-curricular-rg-dev
   
   # Restart frontend  
   az webapp restart --name filtro-curricular-fe-dev --resource-group filtro-curricular-rg-dev
   ```

## 🔍 Verification Steps

After implementing either solution:

1. **Verify Secrets**:
   ```bash
   ./manage-secrets.sh dev verify
   ```

2. **Test Health Endpoints**:
   ```bash
   ./verify-deployment.sh dev health
   ```

3. **Check Application Status**:
   ```bash
   curl https://filtro-curricular-be-dev.azurewebsites.net/status
   curl https://filtro-curricular-be-dev.azurewebsites.net/health
   ```

## 📋 Expected Results After Fix

✅ **Key Vault Secrets**: All 4 OpenAI secrets present with real values  
✅ **App Settings**: Key Vault references configured  
✅ **Health Endpoints**: Return HTTP 200  
✅ **OpenAI Integration**: Backend can connect to OpenAI services  

## 🛠️ Enhanced Script Features

The updated `manage-secrets.sh` now provides:

- **Multi-environment support**: `dev`, `staging`, `uat`, `prod`
- **Terraform integration**: Auto-discovers resources from state
- **GitHub Actions validation**: Checks alignment with CI/CD pipeline
- **Enhanced validation**: Proper OpenAI credential format checking
- **Comprehensive verification**: Tests all integration points

## 🎯 Long-term Recommendations

1. **Standardize on GitHub Secrets**: Use GitHub environment secrets as single source of truth
2. **Automate Secret Rotation**: Implement automated OpenAI key rotation
3. **Monitor Secret Health**: Set up alerts for secret expiration/access issues
4. **Document Secret Management**: Create runbooks for secret management procedures

## 🚀 Next Steps Priority

1. **🔥 IMMEDIATE**: Configure GitHub secrets OR use script to update Key Vault
2. **🔧 HIGH**: Restart applications to pick up new secrets
3. **✅ MEDIUM**: Verify all endpoints return HTTP 200
4. **📊 LOW**: Implement monitoring and alerting for secret health

## 📞 Support Commands

```bash
# Quick verification
./manage-secrets.sh dev verify

# List current secrets
./manage-secrets.sh dev list

# Test secret access
./manage-secrets.sh dev test

# Full deployment verification
./verify-deployment.sh dev

# Show help
./manage-secrets.sh help
```

---

**⏰ Estimated Fix Time**: 15-30 minutes  
**🎯 Success Criteria**: HTTP 200 responses from health endpoints  
**🔄 Rollback Plan**: Revert to previous Terraform state if needed
