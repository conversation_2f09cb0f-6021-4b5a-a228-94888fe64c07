{"_comment": "Azure Key Vault Secrets Template for Filtro Curricular Application", "_instructions": ["This template shows the structure and format of all required secrets", "Replace placeholder values with your actual credentials", "DO NOT commit this file with real values to version control", "Use the manage-secrets.sh script to upload secrets securely"], "_security_note": "All values shown here are examples/placeholders - replace with real credentials", "openai_api_key": {"value": "********************************************************************************************************************************************************************", "description": "OpenAI API Key for GPT models", "format": "sk-proj-[64-character-string]", "required": true, "used_by": ["backend"], "azure_keyvault_name": "openai-api-key", "app_setting_name": "GPT_API_KEY"}, "openai_token": {"value": "********************************************************************************************************************************************************************", "description": "OpenAI Token for Assistant API", "format": "sk-[64-character-string]", "required": true, "used_by": ["backend"], "azure_keyvault_name": "openai-token", "app_setting_name": "OPENAI_TOKEN"}, "assistant_id_juridico": {"value": "asst_1234567890abcdefghijklmnop", "description": "OpenAI Assistant ID for Juridico domain expertise", "format": "asst_[24-character-string]", "required": true, "used_by": ["backend"], "azure_keyvault_name": "assistant-id-juridico", "app_setting_name": "ASSISTANT_ID_JURIDICO"}, "assistant_id_calidad": {"value": "asst_abcdefghijklmnop1234567890", "description": "OpenAI Assistant ID for Calidad domain expertise", "format": "asst_[24-character-string]", "required": true, "used_by": ["backend"], "azure_keyvault_name": "assistant-<PERSON>-<PERSON><PERSON><PERSON>", "app_setting_name": "ASSISTANT_ID_CALIDAD"}, "gpt_model": {"value": "gpt-4o-mini", "description": "GPT model to use for completions", "format": "gpt-4o-mini | gpt-4o | gpt-4-turbo | gpt-3.5-turbo", "required": false, "used_by": ["backend"], "azure_keyvault_name": "gpt-model", "app_setting_name": "GPT_MODEL", "default": "gpt-4o-mini"}, "_optional_secrets": {"_comment": "These secrets are optional and can be added if needed", "smtp_username": {"value": "<EMAIL>", "description": "SMTP username for email notifications", "format": "<EMAIL>", "required": false, "used_by": ["backend"], "azure_keyvault_name": "smtp-username", "app_setting_name": "SMTP_USERNAME"}, "smtp_password": {"value": "your-smtp-password-here", "description": "SMTP password for email notifications", "format": "string", "required": false, "used_by": ["backend"], "azure_keyvault_name": "smtp-password", "app_setting_name": "SMTP_PASSWORD"}, "teams_webhook_url": {"value": "https://outlook.office.com/webhook/your-webhook-id/IncomingWebhook/your-webhook-token", "description": "Microsoft Teams webhook URL for notifications", "format": "https://outlook.office.com/webhook/...", "required": false, "used_by": ["github-actions"], "azure_keyvault_name": "teams-webhook-url", "github_secret_name": "TEAMS_WEBHOOK_URL"}, "custom_domain_ssl_cert": {"value": "-----BEGIN CERTIFICATE-----\nMIIC...\n-----END CERTIFICATE-----", "description": "SSL certificate for custom domain", "format": "PEM format certificate", "required": false, "used_by": ["frontend"], "azure_keyvault_name": "ssl-certificate", "app_setting_name": "SSL_CERTIFICATE"}}, "_auto_generated_secrets": {"_comment": "These secrets are automatically generated by Terraform - do not set manually", "storage_connection_string": {"description": "Azure Storage Account connection string", "azure_keyvault_name": "storage-connection-string", "generated_by": "terraform", "used_by": ["backend"]}, "storage_account_key": {"description": "Azure Storage Account primary access key", "azure_keyvault_name": "storage-account-key", "generated_by": "terraform", "used_by": ["backend"]}, "application_insights_key": {"description": "Application Insights instrumentation key", "app_setting_name": "APPINSIGHTS_INSTRUMENTATIONKEY", "generated_by": "terraform", "used_by": ["backend", "frontend"]}}, "_validation_rules": {"openai_api_key": {"regex": "^sk-proj-[a-zA-Z0-9]{64}$", "length": 69, "starts_with": "sk-proj-"}, "openai_token": {"regex": "^sk-[a-zA-Z0-9]{64}$", "length": 67, "starts_with": "sk-"}, "assistant_id_juridico": {"regex": "^asst_[a-zA-Z0-9]{24}$", "length": 29, "starts_with": "asst_"}, "assistant_id_calidad": {"regex": "^asst_[a-zA-Z0-9]{24}$", "length": 29, "starts_with": "asst_"}}, "_usage_examples": {"using_manage_secrets_script": ["# Interactive update of all OpenAI secrets:", "./scripts/manage-secrets.sh update", "", "# Update secrets from this file (after filling real values):", "./scripts/manage-secrets.sh update-from-file secrets/secrets.json", "", "# List all secrets in Key Vault:", "./scripts/manage-secrets.sh list", "", "# Get info about a specific secret:", "./scripts/manage-secrets.sh info openai-api-key"], "using_azure_cli_directly": ["# Set individual secrets:", "az keyvault secret set --vault-name filtro-kv-dev-xxxxxx --name openai-api-key --value 'sk-proj-...'", "az keyvault secret set --vault-name filtro-kv-dev-xxxxxx --name openai-token --value 'sk-...'", "", "# List all secrets:", "az keyvault secret list --vault-name filtro-kv-dev-xxxxxx --output table", "", "# Get secret value (be careful - this shows the actual secret):", "az keyvault secret show --vault-name filtro-kv-dev-xxxxxx --name openai-api-key --query value -o tsv"]}}