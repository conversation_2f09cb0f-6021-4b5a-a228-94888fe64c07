# 🚀 Filtro Curricular - Comprehensive Deployment Verification Script

## 📋 Overview

The `verify-deployment.sh` script is a comprehensive deployment verification tool that validates the complete CI/CD pipeline and infrastructure deployment for the filtro-curricular application. It provides detailed analysis of GitHub Actions workflows, Terraform configuration, and Azure resource deployment status.

## ✨ Features

### 🔍 **Comprehensive Analysis**
- **GitHub Actions Workflows**: Parses and validates infrastructure and application deployment workflows
- **Terraform Configuration**: Cross-references workflow expectations with actual Terraform resources
- **Azure Resource Verification**: Sequential validation of all deployed Azure resources
- **Health Endpoint Testing**: Tests application endpoints for functionality
- **Networking Validation**: Verifies CORS settings and connectivity

### 📊 **Detailed Reporting**
- **Color-coded Status Indicators**: ✅ PASS, ❌ FAIL, ⚠️ WARN, ℹ️ INFO
- **Comprehensive Logging**: Detailed log files with timestamps
- **Success Rate Calculation**: Percentage-based deployment health score
- **Actionable Recommendations**: Specific commands to fix identified issues

### 🎯 **Environment-Specific Validation**
- Supports multiple environments: `dev`, `staging`, `uat`, `prod`
- Uses environment-specific tfvars configuration
- Validates environment-specific container image tags

## 🚀 Usage

### Basic Usage
```bash
# Full verification of DEV environment (default)
./verify-deployment.sh

# Full verification of specific environment
./verify-deployment.sh staging

# Quick checks for specific components
./verify-deployment.sh dev secrets    # Check Key Vault secrets only
./verify-deployment.sh dev health     # Test health endpoints only
./verify-deployment.sh dev logs       # Check application logs only

# Show troubleshooting guide
./verify-deployment.sh troubleshoot

# Show help
./verify-deployment.sh help
```

### Command Options

| Command | Description |
|---------|-------------|
| `(no command)` | Run full comprehensive verification |
| `secrets` | Quick check of Key Vault secrets only |
| `health` | Quick check of health endpoints only |
| `logs` | Quick check of application logs only |
| `troubleshoot` | Show troubleshooting guide |
| `help` | Show help message |

## 🔍 Verification Components

### 1. **Prerequisites Check**
- ✅ Azure CLI installation and authentication
- ✅ Terraform installation and version
- ✅ Required tools (jq, curl)
- ✅ Environment configuration files

### 2. **Terraform Configuration Analysis**
- ✅ Terraform initialization status
- ✅ Configuration validation
- ✅ State file existence and resource extraction
- ✅ Output variable parsing

### 3. **GitHub Actions Workflow Analysis**
- ✅ Infrastructure workflow file existence and configuration
- ✅ Application workflow file existence and triggers
- ✅ Environment protection configuration
- ✅ Infrastructure dependency checks
- ✅ Cross-reference with Terraform outputs

### 4. **Azure Resource Verification**

#### **Container Registry (ACR)**
- ✅ Registry existence and configuration
- ✅ Container image availability
- ✅ Environment-specific image tags
- ✅ Repository structure validation

#### **App Services**
- ✅ Backend and frontend app service status
- ✅ Container configuration validation
- ✅ ACR integration verification
- ✅ Managed identity configuration

#### **Key Vault**
- ✅ Key Vault existence and accessibility
- ✅ Required secrets validation
- ✅ Placeholder detection
- ✅ Access policy verification for app services

#### **Storage Account**
- ✅ Storage account existence and configuration
- ✅ Required container validation (uploads, logs)
- ✅ Access permissions

### 5. **Health Endpoint Testing**
- ✅ Backend `/status` endpoint
- ✅ Backend `/health` endpoint
- ✅ Frontend application accessibility
- ✅ HTTP response code validation

### 6. **Networking and CORS Verification**
- ✅ CORS configuration for backend
- ✅ Frontend URL in CORS origins
- ✅ Development access (localhost) configuration
- ✅ App Service Plan validation

### 7. **Application Logs Analysis**
- ✅ Logging configuration status
- ✅ Recent log retrieval
- ✅ Error pattern detection
- ✅ OpenAI integration activity detection

## 📊 Sample Output

```
================================================================================
🚀 Filtro Curricular - Comprehensive Deployment Verification
================================================================================

  ℹ️  INFO: Starting verification for environment: dev
  ℹ️  INFO: Log file: deployment-verification-20250617-213339.log

------------------------------------------------------------
🔧 Prerequisites Check
------------------------------------------------------------
  ✅ PASS: Azure CLI is installed
  ✅ PASS: Azure CLI is authenticated
  ✅ PASS: Terraform is installed (Version: 1.12.1)
  ✅ PASS: jq is installed
  ✅ PASS: Environment configuration file exists

------------------------------------------------------------
📊 Deployment Status Report
------------------------------------------------------------
  ℹ️  INFO: Verification Summary
      Total Checks: 54
      Passed: 35
      Failed: 4
      Warnings: 6
      Success Rate: 66%
```

## 🔧 Troubleshooting

The script provides specific troubleshooting recommendations based on detected issues:

### Common Issues and Solutions

1. **Missing Secrets**
   ```bash
   ./scripts/manage-secrets.sh update
   ```

2. **Application Not Responding (HTTP 503)**
   ```bash
   az webapp restart --name filtro-curricular-be-dev --resource-group filtro-curricular-rg-dev
   ```

3. **Infrastructure Issues**
   ```bash
   terraform plan -var-file=environments/dev.tfvars
   terraform apply -var-file=environments/dev.tfvars
   ```

4. **Container Deployment Issues**
   ```bash
   gh workflow run filtro-curricular-applications.yml --field environment=dev
   ```

## 📁 Output Files

- **Console Output**: Color-coded real-time verification results
- **Log File**: Detailed timestamped log saved to `scripts/deployment-verification-YYYYMMDD-HHMMSS.log`
- **Exit Codes**: 
  - `0`: Success (no critical failures)
  - `1`: Failure (critical issues found)

## 🎯 Integration with CI/CD

This script can be integrated into CI/CD pipelines for:
- **Post-deployment validation**
- **Health checks before production releases**
- **Automated infrastructure testing**
- **Troubleshooting assistance**

## 📝 Notes

- The script requires appropriate Azure permissions to read resources
- Some checks may require additional permissions (e.g., log access)
- The script is designed to be non-destructive (read-only operations)
- Detailed logging helps with troubleshooting and audit trails
