#!/bin/bash

# Azure Region Migration Script: Chile Central → Brazil South
# This script helps migrate Azure infrastructure from chilecentral to brazilsouth

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
OLD_REGION="chilecentral"
NEW_REGION="brazilsouth"
BACKUP_DIR="./migration-backups"
DATE_STAMP=$(date +%Y%m%d_%H%M%S)

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "\n${BLUE}================================${NC}"
    echo -e "${BLUE} $1${NC}"
    echo -e "${BLUE}================================${NC}\n"
}

# Function to check prerequisites
check_prerequisites() {
    print_header "Checking Prerequisites"
    
    # Check if Azure CLI is installed
    if ! command -v az &> /dev/null; then
        print_error "Azure CLI is not installed. Please install it first."
        exit 1
    fi
    
    # Check if Terraform is installed
    if ! command -v terraform &> /dev/null; then
        print_error "Terraform is not installed. Please install it first."
        exit 1
    fi
    
    # Check if logged into Azure
    if ! az account show &> /dev/null; then
        print_error "Not logged into Azure. Please run 'az login' first."
        exit 1
    fi
    
    # Check if in correct directory
    if [[ ! -f "main.tf" ]]; then
        print_error "Please run this script from the Terraform directory (apps/infrastructure/azure/filtro-curricular)"
        exit 1
    fi
    
    print_success "All prerequisites met"
}

# Function to create backups
create_backups() {
    print_header "Creating Backups"
    
    # Create backup directory
    mkdir -p "$BACKUP_DIR"
    
    # Backup Terraform state
    print_status "Backing up Terraform state..."
    if terraform state pull > "$BACKUP_DIR/terraform-state-$DATE_STAMP.json" 2>/dev/null; then
        print_success "Terraform state backed up"
    else
        print_warning "Could not backup Terraform state (may not exist yet)"
    fi
    
    # Backup current resource group (if exists)
    print_status "Backing up current resources..."
    RESOURCE_GROUP=$(terraform output -raw resource_group_name 2>/dev/null || echo "filtro-curricular-rg-dev")
    
    if az group show --name "$RESOURCE_GROUP" &> /dev/null; then
        az group export --name "$RESOURCE_GROUP" --include-comments > "$BACKUP_DIR/resources-$DATE_STAMP.json"
        print_success "Resources backed up"
    else
        print_warning "Resource group $RESOURCE_GROUP not found (may be first deployment)"
    fi
    
    # Backup configuration files
    print_status "Backing up configuration files..."
    cp terraform.tfvars "$BACKUP_DIR/terraform.tfvars-$DATE_STAMP.backup" 2>/dev/null || true
    cp variables.tf "$BACKUP_DIR/variables.tf-$DATE_STAMP.backup" 2>/dev/null || true
    
    print_success "Backups created in $BACKUP_DIR"
}

# Function to validate new region
validate_new_region() {
    print_header "Validating New Region: $NEW_REGION"
    
    # Check if region exists
    if ! az account list-locations --query "[?name=='$NEW_REGION']" --output tsv | grep -q "$NEW_REGION"; then
        print_error "Region $NEW_REGION not found or not accessible"
        exit 1
    fi
    
    # Check Container Registry availability
    print_status "Checking Container Registry availability in $NEW_REGION..."
    if az provider show --namespace Microsoft.ContainerRegistry --query "resourceTypes[?resourceType=='registries'].locations" --output tsv | grep -qi "$NEW_REGION"; then
        print_success "Container Registry available in $NEW_REGION"
    else
        print_error "Container Registry not available in $NEW_REGION"
        exit 1
    fi
    
    # Check App Service availability
    print_status "Checking App Service availability in $NEW_REGION..."
    if az provider show --namespace Microsoft.Web --query "resourceTypes[?resourceType=='sites'].locations" --output tsv | grep -qi "$NEW_REGION"; then
        print_success "App Service available in $NEW_REGION"
    else
        print_error "App Service not available in $NEW_REGION"
        exit 1
    fi
    
    print_success "Region $NEW_REGION validation complete"
}

# Function to plan migration
plan_migration() {
    print_header "Planning Migration"
    
    print_status "Initializing Terraform..."
    terraform init
    
    print_status "Planning migration to $NEW_REGION..."
    print_warning "This will show resources that need to be recreated"
    
    if terraform plan -var-file="terraform.tfvars" -out="migration-plan-$DATE_STAMP.tfplan"; then
        print_success "Migration plan created: migration-plan-$DATE_STAMP.tfplan"
        print_warning "Review the plan carefully before proceeding"
    else
        print_error "Failed to create migration plan"
        exit 1
    fi
}

# Function to execute migration
execute_migration() {
    print_header "Executing Migration"
    
    print_warning "This will recreate resources in the new region"
    read -p "Are you sure you want to proceed? (yes/no): " confirm
    
    if [[ $confirm != "yes" ]]; then
        print_status "Migration cancelled by user"
        exit 0
    fi
    
    print_status "Applying migration plan..."
    if terraform apply "migration-plan-$DATE_STAMP.tfplan"; then
        print_success "Migration completed successfully!"
    else
        print_error "Migration failed. Check Terraform output for details."
        print_status "You can restore from backup if needed"
        exit 1
    fi
}

# Function to verify migration
verify_migration() {
    print_header "Verifying Migration"
    
    # Get new resource names
    print_status "Getting new resource information..."
    RESOURCE_GROUP=$(terraform output -raw resource_group_name)
    ACR_NAME=$(terraform output -raw container_registry_name)
    FRONTEND_APP=$(terraform output -raw frontend_app_name)
    BACKEND_APP=$(terraform output -raw backend_app_name)
    
    # Check resource group
    print_status "Checking resource group: $RESOURCE_GROUP"
    if az group show --name "$RESOURCE_GROUP" --query "location" --output tsv | grep -q "$NEW_REGION"; then
        print_success "Resource group created in $NEW_REGION"
    else
        print_error "Resource group not in expected region"
    fi
    
    # Check Container Registry
    print_status "Checking Container Registry: $ACR_NAME"
    if az acr show --name "$ACR_NAME" --query "location" --output tsv | grep -q "$NEW_REGION"; then
        print_success "Container Registry created in $NEW_REGION"
        
        # Test ACR Task Runs API (the reason for migration)
        print_status "Testing ACR Task Runs API..."
        if az acr task list-runs --registry "$ACR_NAME" --output table &> /dev/null; then
            print_success "ACR Task Runs API working! 🎉"
        else
            print_warning "ACR Task Runs API test failed (may be expected if no tasks exist)"
        fi
    else
        print_error "Container Registry not in expected region"
    fi
    
    # Check App Services
    print_status "Checking App Services..."
    if az webapp show --name "$FRONTEND_APP" --resource-group "$RESOURCE_GROUP" --query "location" --output tsv | grep -q "$NEW_REGION"; then
        print_success "Frontend App Service created in $NEW_REGION"
    else
        print_error "Frontend App Service not in expected region"
    fi
    
    if az webapp show --name "$BACKEND_APP" --resource-group "$RESOURCE_GROUP" --query "location" --output tsv | grep -q "$NEW_REGION"; then
        print_success "Backend App Service created in $NEW_REGION"
    else
        print_error "Backend App Service not in expected region"
    fi
    
    print_success "Migration verification complete!"
}

# Function to show next steps
show_next_steps() {
    print_header "Next Steps"
    
    echo -e "1. ${GREEN}Update GitHub Actions secrets${NC} (if needed)"
    echo -e "2. ${GREEN}Redeploy applications${NC} to populate new Container Registry"
    echo -e "3. ${GREEN}Test application functionality${NC}"
    echo -e "4. ${GREEN}Update DNS records${NC} (if using custom domains)"
    echo -e "5. ${GREEN}Monitor performance${NC} and user experience"
    echo ""
    echo -e "New resource information:"
    echo -e "  Resource Group: ${BLUE}$(terraform output -raw resource_group_name)${NC}"
    echo -e "  Container Registry: ${BLUE}$(terraform output -raw container_registry_name)${NC}"
    echo -e "  Frontend URL: ${BLUE}$(terraform output -raw frontend_url)${NC}"
    echo -e "  Backend URL: ${BLUE}$(terraform output -raw backend_url)${NC}"
    echo ""
    echo -e "To trigger application redeployment:"
    echo -e "  ${YELLOW}git add .${NC}"
    echo -e "  ${YELLOW}git commit -m \"migrate: Move infrastructure to Brazil South region\"${NC}"
    echo -e "  ${YELLOW}git push origin main${NC}"
}

# Main execution
main() {
    print_header "Azure Region Migration: $OLD_REGION → $NEW_REGION"
    
    case "${1:-}" in
        "check")
            check_prerequisites
            ;;
        "backup")
            check_prerequisites
            create_backups
            ;;
        "validate")
            check_prerequisites
            validate_new_region
            ;;
        "plan")
            check_prerequisites
            create_backups
            validate_new_region
            plan_migration
            ;;
        "migrate")
            check_prerequisites
            create_backups
            validate_new_region
            plan_migration
            execute_migration
            verify_migration
            show_next_steps
            ;;
        "verify")
            verify_migration
            ;;
        *)
            echo "Usage: $0 {check|backup|validate|plan|migrate|verify}"
            echo ""
            echo "Commands:"
            echo "  check     - Check prerequisites"
            echo "  backup    - Create backups only"
            echo "  validate  - Validate new region capabilities"
            echo "  plan      - Plan the migration"
            echo "  migrate   - Execute full migration (includes all steps)"
            echo "  verify    - Verify migration results"
            echo ""
            echo "Recommended workflow:"
            echo "  1. $0 check"
            echo "  2. $0 plan"
            echo "  3. $0 migrate"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
