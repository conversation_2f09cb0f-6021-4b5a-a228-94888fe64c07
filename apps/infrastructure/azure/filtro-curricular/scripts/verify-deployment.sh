#!/bin/bash

# =============================================================================
# Filtro Curricular - Comprehensive Deployment Verification Script
# =============================================================================
# This script validates the complete CI/CD pipeline and infrastructure
# deployment for the filtro-curricular application.
#
# Author: Augment Agent
# Version: 2.0
# Date: 2025-06-18
# =============================================================================

set -euo pipefail

# =============================================================================
# CONFIGURATION AND CONSTANTS
# =============================================================================

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../../../.." && pwd)"
TERRAFORM_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"
LOG_FILE="${SCRIPT_DIR}/deployment-verification-$(date +%Y%m%d-%H%M%S).log"

# Environment configuration
ENVIRONMENT="${1:-dev}"
TFVARS_FILE="${TERRAFORM_DIR}/environments/${ENVIRONMENT}.tfvars"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Status tracking
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0
WARNING_CHECKS=0

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a "$LOG_FILE"
}

print_header() {
    local title="$1"
    local separator=$(printf '%*s' 80 '' | tr ' ' '=')
    echo -e "\n${BLUE}${separator}${NC}"
    echo -e "${BLUE}$title${NC}"
    echo -e "${BLUE}${separator}${NC}\n"
    log "INFO" "$title"
}

print_section() {
    local title="$1"
    local separator=$(printf '%*s' 60 '' | tr ' ' '-')
    echo -e "\n${CYAN}${separator}${NC}"
    echo -e "${CYAN}$title${NC}"
    echo -e "${CYAN}${separator}${NC}"
    log "INFO" "Section: $title"
}

print_status() {
    local status="$1"
    local message="$2"
    local details="${3:-}"

    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))

    case "$status" in
        "PASS")
            echo -e "  ${GREEN}✅ PASS${NC}: $message"
            PASSED_CHECKS=$((PASSED_CHECKS + 1))
            log "PASS" "$message"
            ;;
        "FAIL")
            echo -e "  ${RED}❌ FAIL${NC}: $message"
            FAILED_CHECKS=$((FAILED_CHECKS + 1))
            log "FAIL" "$message"
            ;;
        "WARN")
            echo -e "  ${YELLOW}⚠️  WARN${NC}: $message"
            WARNING_CHECKS=$((WARNING_CHECKS + 1))
            log "WARN" "$message"
            ;;
        "INFO")
            echo -e "  ${BLUE}ℹ️  INFO${NC}: $message"
            log "INFO" "$message"
            ;;
    esac

    if [[ -n "$details" ]]; then
        echo -e "      ${details}"
        log "DETAILS" "$details"
    fi
}

check_prerequisites() {
    print_section "🔧 Prerequisites Check"

    # Check if Azure CLI is installed and logged in
    if command -v az &> /dev/null; then
        print_status "PASS" "Azure CLI is installed"

        if az account show &> /dev/null; then
            local account_info=$(az account show --query "{name:name, id:id}" -o tsv)
            print_status "PASS" "Azure CLI is authenticated" "$account_info"
        else
            print_status "FAIL" "Azure CLI is not authenticated"
            return 1
        fi
    else
        print_status "FAIL" "Azure CLI is not installed"
        return 1
    fi

    # Check if Terraform is installed
    if command -v terraform &> /dev/null; then
        local tf_version=$(terraform version -json | jq -r '.terraform_version')
        print_status "PASS" "Terraform is installed" "Version: $tf_version"
    else
        print_status "FAIL" "Terraform is not installed"
        return 1
    fi

    # Check if jq is installed
    if command -v jq &> /dev/null; then
        print_status "PASS" "jq is installed"
    else
        print_status "FAIL" "jq is not installed (required for JSON parsing)"
        return 1
    fi

    # Check if environment tfvars file exists
    if [[ -f "$TFVARS_FILE" ]]; then
        print_status "PASS" "Environment configuration file exists" "$TFVARS_FILE"
    else
        print_status "FAIL" "Environment configuration file not found" "$TFVARS_FILE"
        return 1
    fi
}

parse_terraform_config() {
    print_section "📋 Terraform Configuration Analysis"

    cd "$TERRAFORM_DIR"

    # Initialize Terraform if needed
    if [[ ! -d ".terraform" ]]; then
        print_status "INFO" "Initializing Terraform..."
        if terraform init &> /dev/null; then
            print_status "PASS" "Terraform initialized successfully"
        else
            print_status "FAIL" "Failed to initialize Terraform"
            return 1
        fi
    else
        print_status "PASS" "Terraform already initialized"
    fi

    # Validate Terraform configuration
    if terraform validate &> /dev/null; then
        print_status "PASS" "Terraform configuration is valid"
    else
        print_status "FAIL" "Terraform configuration validation failed"
        return 1
    fi

    # Get Terraform outputs (if state exists)
    if terraform state list &> /dev/null && [[ $(terraform state list | wc -l) -gt 0 ]]; then
        print_status "PASS" "Terraform state exists with resources"

        # Extract key resource information
        export TF_RESOURCE_GROUP=$(terraform output -raw resource_group_name 2>/dev/null || echo "")
        export TF_ACR_NAME=$(terraform output -raw container_registry_name 2>/dev/null || echo "")
        export TF_ACR_LOGIN_SERVER=$(terraform output -raw container_registry_login_server 2>/dev/null || echo "")
        export TF_BACKEND_APP_NAME=$(terraform output -raw backend_app_service_name 2>/dev/null || echo "")
        export TF_FRONTEND_APP_NAME=$(terraform output -raw frontend_app_service_name 2>/dev/null || echo "")
        export TF_BACKEND_URL=$(terraform output -raw backend_app_service_url 2>/dev/null || echo "")
        export TF_FRONTEND_URL=$(terraform output -raw frontend_app_service_url 2>/dev/null || echo "")
        export TF_KEY_VAULT_NAME=$(terraform output -raw key_vault_name 2>/dev/null || echo "")
        export TF_STORAGE_ACCOUNT_NAME=$(terraform output -raw storage_account_name 2>/dev/null || echo "")

        print_status "INFO" "Extracted Terraform outputs" "Resource Group: $TF_RESOURCE_GROUP"
    else
        print_status "WARN" "No Terraform state found or state is empty"

        # Parse expected values from tfvars
        export TF_RESOURCE_GROUP="filtro-curricular-rg-${ENVIRONMENT}"
        export TF_ACR_NAME=""  # Will be generated with random suffix
        export TF_BACKEND_APP_NAME="filtro-curricular-be-${ENVIRONMENT}"
        export TF_FRONTEND_APP_NAME="filtro-curricular-fe-${ENVIRONMENT}"

        print_status "INFO" "Using expected resource names from configuration"
    fi
}

analyze_github_workflows() {
    print_section "🔄 GitHub Actions Workflow Analysis"

    local infra_workflow="${PROJECT_ROOT}/.github/workflows/filtro-curricular-infrastructure.yml"
    local app_workflow="${PROJECT_ROOT}/.github/workflows/filtro-curricular-applications.yml"

    # Check if workflow files exist
    if [[ -f "$infra_workflow" ]]; then
        print_status "PASS" "Infrastructure workflow file exists" "$infra_workflow"

        # Parse workflow configuration
        local terraform_version=$(grep -o "TERRAFORM_VERSION:.*" "$infra_workflow" | cut -d"'" -f2 || echo "Not specified")
        local working_dir=$(grep -o "WORKING_DIRECTORY:.*" "$infra_workflow" | cut -d"'" -f2 || echo "Not specified")

        print_status "INFO" "Infrastructure workflow configuration" "Terraform: $terraform_version, Working Dir: $working_dir"

        # Check environment configuration
        if grep -q "environment:" "$infra_workflow"; then
            print_status "PASS" "Environment protection configured in infrastructure workflow"
        else
            print_status "WARN" "No environment protection found in infrastructure workflow"
        fi
    else
        print_status "FAIL" "Infrastructure workflow file not found" "$infra_workflow"
    fi

    if [[ -f "$app_workflow" ]]; then
        print_status "PASS" "Application workflow file exists" "$app_workflow"

        # Parse application workflow triggers
        local backend_path=$(grep -A10 "paths:" "$app_workflow" | grep "filtro-curricular-be-api" || echo "Not found")
        local frontend_path=$(grep -A10 "paths:" "$app_workflow" | grep "filtro-curricular-fe-web" || echo "Not found")

        if [[ "$backend_path" != "Not found" ]] && [[ "$frontend_path" != "Not found" ]]; then
            print_status "PASS" "Application workflow paths configured correctly"
        else
            print_status "WARN" "Application workflow paths may not be configured correctly"
        fi

        # Check for infrastructure dependency
        if grep -q "check-infrastructure-workflow" "$app_workflow"; then
            print_status "PASS" "Application workflow has infrastructure dependency check"
        else
            print_status "WARN" "No infrastructure dependency check found in application workflow"
        fi
    else
        print_status "FAIL" "Application workflow file not found" "$app_workflow"
    fi

    # Cross-reference with Terraform configuration
    if [[ -n "$TF_RESOURCE_GROUP" ]]; then
        print_status "INFO" "Cross-referencing workflow expectations with Terraform outputs"
        print_status "INFO" "Expected resource group from Terraform: $TF_RESOURCE_GROUP"

        # Check if workflow environment matches
        if grep -q "$ENVIRONMENT" "$infra_workflow" 2>/dev/null; then
            print_status "PASS" "Environment '$ENVIRONMENT' is configured in infrastructure workflow"
        else
            print_status "WARN" "Environment '$ENVIRONMENT' may not be configured in infrastructure workflow"
        fi
    fi
}

verify_azure_resources() {
    print_section "☁️  Azure Resource Verification"

    # Verify Resource Group
    if [[ -n "$TF_RESOURCE_GROUP" ]]; then
        if az group show --name "$TF_RESOURCE_GROUP" &> /dev/null; then
            local rg_location=$(az group show --name "$TF_RESOURCE_GROUP" --query location -o tsv)
            print_status "PASS" "Resource Group exists" "$TF_RESOURCE_GROUP in $rg_location"
        else
            print_status "FAIL" "Resource Group not found" "$TF_RESOURCE_GROUP"
        fi
    else
        print_status "WARN" "Resource Group name not available for verification"
    fi

    # Verify Container Registry
    verify_container_registry

    # Verify App Services
    verify_app_services

    # Verify Key Vault
    verify_key_vault

    # Verify Storage Account
    verify_storage_account
}

verify_container_registry() {
    print_section "📦 Container Registry Verification"

    if [[ -n "$TF_RESOURCE_GROUP" ]]; then
        # Find ACR in resource group if name not available
        if [[ -z "$TF_ACR_NAME" ]]; then
            TF_ACR_NAME=$(az acr list --resource-group "$TF_RESOURCE_GROUP" --query "[0].name" -o tsv 2>/dev/null || echo "")
        fi

        if [[ -n "$TF_ACR_NAME" ]]; then
            if az acr show --name "$TF_ACR_NAME" --resource-group "$TF_RESOURCE_GROUP" &> /dev/null; then
                local acr_sku=$(az acr show --name "$TF_ACR_NAME" --resource-group "$TF_RESOURCE_GROUP" --query sku.name -o tsv)
                local acr_login_server=$(az acr show --name "$TF_ACR_NAME" --resource-group "$TF_RESOURCE_GROUP" --query loginServer -o tsv)
                print_status "PASS" "Container Registry exists" "$TF_ACR_NAME ($acr_sku) - $acr_login_server"

                # Update login server if not set
                if [[ -z "$TF_ACR_LOGIN_SERVER" ]]; then
                    export TF_ACR_LOGIN_SERVER="$acr_login_server"
                fi

                # Check for container images
                verify_container_images "$TF_ACR_NAME"
            else
                print_status "FAIL" "Container Registry not found" "$TF_ACR_NAME"
            fi
        else
            print_status "WARN" "Container Registry name not available for verification"
        fi
    else
        print_status "WARN" "Cannot verify Container Registry without Resource Group"
    fi
}

verify_container_images() {
    local acr_name="$1"

    print_section "🐳 Container Images Verification"

    # Check backend image
    local backend_repos=$(az acr repository list --name "$acr_name" --query "[?contains(@, 'filtro-curricular-be-api')]" -o tsv 2>/dev/null || echo "")
    if [[ -n "$backend_repos" ]]; then
        local backend_tags=$(az acr repository show-tags --name "$acr_name" --repository "filtro-curricular-be-api" --orderby time_desc --top 3 -o tsv 2>/dev/null || echo "")
        print_status "PASS" "Backend container images found" "Tags: $backend_tags"

        # Check for environment-specific tags
        if echo "$backend_tags" | grep -q "${ENVIRONMENT}-latest"; then
            print_status "PASS" "Environment-specific backend tag exists" "${ENVIRONMENT}-latest"
        else
            print_status "WARN" "No environment-specific backend tag found" "Expected: ${ENVIRONMENT}-latest"
        fi
    else
        print_status "WARN" "No backend container images found" "Repository: filtro-curricular-be-api"
    fi

    # Check frontend image
    local frontend_repos=$(az acr repository list --name "$acr_name" --query "[?contains(@, 'filtro-curricular-fe-web')]" -o tsv 2>/dev/null || echo "")
    if [[ -n "$frontend_repos" ]]; then
        local frontend_tags=$(az acr repository show-tags --name "$acr_name" --repository "filtro-curricular-fe-web" --orderby time_desc --top 3 -o tsv 2>/dev/null || echo "")
        print_status "PASS" "Frontend container images found" "Tags: $frontend_tags"

        # Check for environment-specific tags
        if echo "$frontend_tags" | grep -q "${ENVIRONMENT}-latest"; then
            print_status "PASS" "Environment-specific frontend tag exists" "${ENVIRONMENT}-latest"
        else
            print_status "WARN" "No environment-specific frontend tag found" "Expected: ${ENVIRONMENT}-latest"
        fi
    else
        print_status "WARN" "No frontend container images found" "Repository: filtro-curricular-fe-web"
    fi
}

verify_app_services() {
    print_section "🌐 App Services Verification"

    # Find app services if names not available
    if [[ -n "$TF_RESOURCE_GROUP" ]]; then
        if [[ -z "$TF_BACKEND_APP_NAME" ]]; then
            TF_BACKEND_APP_NAME=$(az webapp list --resource-group "$TF_RESOURCE_GROUP" --query "[?contains(name, 'be-')].name" -o tsv 2>/dev/null || echo "")
        fi

        if [[ -z "$TF_FRONTEND_APP_NAME" ]]; then
            TF_FRONTEND_APP_NAME=$(az webapp list --resource-group "$TF_RESOURCE_GROUP" --query "[?contains(name, 'fe-')].name" -o tsv 2>/dev/null || echo "")
        fi
    fi

    # Verify Backend App Service
    if [[ -n "$TF_BACKEND_APP_NAME" ]] && [[ -n "$TF_RESOURCE_GROUP" ]]; then
        if az webapp show --name "$TF_BACKEND_APP_NAME" --resource-group "$TF_RESOURCE_GROUP" &> /dev/null; then
            local backend_state=$(az webapp show --name "$TF_BACKEND_APP_NAME" --resource-group "$TF_RESOURCE_GROUP" --query state -o tsv)
            local backend_url="https://${TF_BACKEND_APP_NAME}.azurewebsites.net"

            if [[ "$backend_state" == "Running" ]]; then
                print_status "PASS" "Backend App Service is running" "$TF_BACKEND_APP_NAME - $backend_url"
            else
                print_status "FAIL" "Backend App Service is not running" "State: $backend_state"
            fi

            # Check container configuration
            verify_app_service_container_config "$TF_BACKEND_APP_NAME" "backend"
        else
            print_status "FAIL" "Backend App Service not found" "$TF_BACKEND_APP_NAME"
        fi
    else
        print_status "WARN" "Backend App Service name not available for verification"
    fi

    # Verify Frontend App Service
    if [[ -n "$TF_FRONTEND_APP_NAME" ]] && [[ -n "$TF_RESOURCE_GROUP" ]]; then
        if az webapp show --name "$TF_FRONTEND_APP_NAME" --resource-group "$TF_RESOURCE_GROUP" &> /dev/null; then
            local frontend_state=$(az webapp show --name "$TF_FRONTEND_APP_NAME" --resource-group "$TF_RESOURCE_GROUP" --query state -o tsv)
            local frontend_url="https://${TF_FRONTEND_APP_NAME}.azurewebsites.net"

            if [[ "$frontend_state" == "Running" ]]; then
                print_status "PASS" "Frontend App Service is running" "$TF_FRONTEND_APP_NAME - $frontend_url"
            else
                print_status "FAIL" "Frontend App Service is not running" "State: $frontend_state"
            fi

            # Check container configuration
            verify_app_service_container_config "$TF_FRONTEND_APP_NAME" "frontend"
        else
            print_status "FAIL" "Frontend App Service not found" "$TF_FRONTEND_APP_NAME"
        fi
    else
        print_status "WARN" "Frontend App Service name not available for verification"
    fi
}

verify_app_service_container_config() {
    local app_name="$1"
    local app_type="$2"

    # Check container configuration
    local container_config=$(az webapp config show --name "$app_name" --resource-group "$TF_RESOURCE_GROUP" --query "linuxFxVersion" -o tsv 2>/dev/null || echo "")

    if [[ -n "$container_config" ]] && [[ "$container_config" != "null" ]]; then
        print_status "PASS" "$app_type container configuration found" "$container_config"

        # Check if using ACR
        if [[ "$container_config" == *"$TF_ACR_LOGIN_SERVER"* ]]; then
            print_status "PASS" "$app_type is using correct ACR" "$TF_ACR_LOGIN_SERVER"
        else
            print_status "WARN" "$app_type may not be using expected ACR" "Current: $container_config"
        fi
    else
        print_status "WARN" "No container configuration found for $app_type"
    fi

    # Check managed identity
    local identity=$(az webapp identity show --name "$app_name" --resource-group "$TF_RESOURCE_GROUP" --query "type" -o tsv 2>/dev/null || echo "")
    if [[ "$identity" == "SystemAssigned" ]]; then
        print_status "PASS" "$app_type has system-assigned managed identity"
    else
        print_status "WARN" "$app_type managed identity not configured" "Type: $identity"
    fi
}

verify_key_vault() {
    print_section "🔐 Key Vault Verification"

    # Find Key Vault if name not available
    if [[ -n "$TF_RESOURCE_GROUP" ]]; then
        if [[ -z "$TF_KEY_VAULT_NAME" ]]; then
            TF_KEY_VAULT_NAME=$(az keyvault list --resource-group "$TF_RESOURCE_GROUP" --query "[0].name" -o tsv 2>/dev/null || echo "")
        fi
    fi

    if [[ -n "$TF_KEY_VAULT_NAME" ]] && [[ -n "$TF_RESOURCE_GROUP" ]]; then
        if az keyvault show --name "$TF_KEY_VAULT_NAME" --resource-group "$TF_RESOURCE_GROUP" &> /dev/null; then
            local kv_uri=$(az keyvault show --name "$TF_KEY_VAULT_NAME" --resource-group "$TF_RESOURCE_GROUP" --query properties.vaultUri -o tsv)
            print_status "PASS" "Key Vault exists" "$TF_KEY_VAULT_NAME - $kv_uri"

            # Verify secrets
            verify_key_vault_secrets "$TF_KEY_VAULT_NAME"

            # Check access policies
            verify_key_vault_access_policies "$TF_KEY_VAULT_NAME"
        else
            print_status "FAIL" "Key Vault not found" "$TF_KEY_VAULT_NAME"
        fi
    else
        print_status "WARN" "Key Vault name not available for verification"
    fi
}

verify_key_vault_secrets() {
    local kv_name="$1"

    print_section "🔑 Key Vault Secrets Verification"

    local required_secrets=("openai-api-key" "openai-token" "assistant-id-juridico" "assistant-id-calidad")
    local missing_secrets=()

    for secret in "${required_secrets[@]}"; do
        if az keyvault secret show --vault-name "$kv_name" --name "$secret" --query "name" -o tsv &>/dev/null; then
            # Check if it's not a placeholder
            local secret_value=$(az keyvault secret show --vault-name "$kv_name" --name "$secret" --query "value" -o tsv 2>/dev/null || echo "")
            if [[ "$secret_value" == *"placeholder"* ]] || [[ "$secret_value" == *"your-"* ]] || [[ "$secret_value" == *"-here"* ]]; then
                print_status "WARN" "Secret contains placeholder value" "$secret"
                missing_secrets+=("$secret")
            else
                print_status "PASS" "Secret exists and configured" "$secret"
            fi
        else
            print_status "FAIL" "Missing secret" "$secret"
            missing_secrets+=("$secret")
        fi
    done

    if [[ ${#missing_secrets[@]} -gt 0 ]]; then
        print_status "WARN" "Missing or placeholder secrets found" "${missing_secrets[*]}"
        print_status "INFO" "Run: ./scripts/manage-secrets.sh update"
    else
        print_status "PASS" "All required secrets are properly configured"
    fi
}

verify_key_vault_access_policies() {
    local kv_name="$1"

    # Check if app services have access policies
    if [[ -n "$TF_BACKEND_APP_NAME" ]]; then
        local backend_principal=$(az webapp identity show --name "$TF_BACKEND_APP_NAME" --resource-group "$TF_RESOURCE_GROUP" --query principalId -o tsv 2>/dev/null || echo "")
        if [[ -n "$backend_principal" ]]; then
            local access_policy=$(az keyvault show --name "$kv_name" --query "properties.accessPolicies[?objectId=='$backend_principal']" -o tsv 2>/dev/null || echo "")
            if [[ -n "$access_policy" ]]; then
                print_status "PASS" "Backend app has Key Vault access policy"
            else
                print_status "WARN" "Backend app may not have Key Vault access policy"
            fi
        fi
    fi

    if [[ -n "$TF_FRONTEND_APP_NAME" ]]; then
        local frontend_principal=$(az webapp identity show --name "$TF_FRONTEND_APP_NAME" --resource-group "$TF_RESOURCE_GROUP" --query principalId -o tsv 2>/dev/null || echo "")
        if [[ -n "$frontend_principal" ]]; then
            local access_policy=$(az keyvault show --name "$kv_name" --query "properties.accessPolicies[?objectId=='$frontend_principal']" -o tsv 2>/dev/null || echo "")
            if [[ -n "$access_policy" ]]; then
                print_status "PASS" "Frontend app has Key Vault access policy"
            else
                print_status "WARN" "Frontend app may not have Key Vault access policy"
            fi
        fi
    fi
}

verify_storage_account() {
    print_section "💾 Storage Account Verification"

    # Find Storage Account if name not available
    if [[ -n "$TF_RESOURCE_GROUP" ]]; then
        if [[ -z "$TF_STORAGE_ACCOUNT_NAME" ]]; then
            TF_STORAGE_ACCOUNT_NAME=$(az storage account list --resource-group "$TF_RESOURCE_GROUP" --query "[?contains(name, 'st')].name" -o tsv 2>/dev/null | head -1 || echo "")
        fi
    fi

    if [[ -n "$TF_STORAGE_ACCOUNT_NAME" ]] && [[ -n "$TF_RESOURCE_GROUP" ]]; then
        if az storage account show --name "$TF_STORAGE_ACCOUNT_NAME" --resource-group "$TF_RESOURCE_GROUP" &> /dev/null; then
            local storage_tier=$(az storage account show --name "$TF_STORAGE_ACCOUNT_NAME" --resource-group "$TF_RESOURCE_GROUP" --query sku.tier -o tsv)
            local storage_replication=$(az storage account show --name "$TF_STORAGE_ACCOUNT_NAME" --resource-group "$TF_RESOURCE_GROUP" --query sku.name -o tsv)
            print_status "PASS" "Storage Account exists" "$TF_STORAGE_ACCOUNT_NAME ($storage_tier, $storage_replication)"

            # Check containers
            verify_storage_containers "$TF_STORAGE_ACCOUNT_NAME"
        else
            print_status "FAIL" "Storage Account not found" "$TF_STORAGE_ACCOUNT_NAME"
        fi
    else
        print_status "WARN" "Storage Account name not available for verification"
    fi
}

verify_storage_containers() {
    local storage_name="$1"

    local expected_containers=("uploads" "logs")

    for container in "${expected_containers[@]}"; do
        if az storage container show --name "$container" --account-name "$storage_name" --auth-mode login &> /dev/null; then
            print_status "PASS" "Storage container exists" "$container"
        else
            print_status "WARN" "Storage container not found" "$container"
        fi
    done
}

test_health_endpoints() {
    print_section "🏥 Health Endpoints Testing"

    # Test Backend Health Endpoints
    if [[ -n "$TF_BACKEND_APP_NAME" ]]; then
        local backend_url="https://${TF_BACKEND_APP_NAME}.azurewebsites.net"

        print_status "INFO" "Testing backend endpoints" "$backend_url"

        # Test /status endpoint
        if command -v curl &> /dev/null; then
            local status_response=$(curl -s -w "HTTP_CODE:%{http_code}" "$backend_url/status" 2>/dev/null || echo "HTTP_CODE:000")
            local status_code=$(echo "$status_response" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
            local status_body=$(echo "$status_response" | sed 's/HTTP_CODE:[0-9]*$//')

            if [[ "$status_code" == "200" ]]; then
                print_status "PASS" "Backend /status endpoint responding" "HTTP $status_code"
            elif [[ "$status_code" == "000" ]]; then
                print_status "WARN" "Could not connect to backend /status endpoint" "Check if app is running"
            else
                print_status "WARN" "Backend /status endpoint returned HTTP $status_code"
            fi

            # Test /health endpoint
            local health_response=$(curl -s -w "HTTP_CODE:%{http_code}" "$backend_url/health" 2>/dev/null || echo "HTTP_CODE:000")
            local health_code=$(echo "$health_response" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)

            if [[ "$health_code" == "200" ]]; then
                print_status "PASS" "Backend /health endpoint responding" "HTTP $health_code"
            elif [[ "$health_code" == "000" ]]; then
                print_status "WARN" "Could not connect to backend /health endpoint"
            else
                print_status "WARN" "Backend /health endpoint returned HTTP $health_code"
            fi
        else
            print_status "WARN" "curl not available for endpoint testing"
        fi
    fi

    # Test Frontend
    if [[ -n "$TF_FRONTEND_APP_NAME" ]]; then
        local frontend_url="https://${TF_FRONTEND_APP_NAME}.azurewebsites.net"

        if command -v curl &> /dev/null; then
            local frontend_response=$(curl -s -w "HTTP_CODE:%{http_code}" "$frontend_url" 2>/dev/null || echo "HTTP_CODE:000")
            local frontend_code=$(echo "$frontend_response" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)

            if [[ "$frontend_code" == "200" ]]; then
                print_status "PASS" "Frontend application responding" "HTTP $frontend_code"
            elif [[ "$frontend_code" == "000" ]]; then
                print_status "WARN" "Could not connect to frontend application"
            else
                print_status "WARN" "Frontend application returned HTTP $frontend_code"
            fi
        fi
    fi
}

verify_networking_and_cors() {
    print_section "🌐 Networking and CORS Verification"

    # Check CORS configuration for backend
    if [[ -n "$TF_BACKEND_APP_NAME" ]] && [[ -n "$TF_RESOURCE_GROUP" ]]; then
        local cors_config=$(az webapp cors show --name "$TF_BACKEND_APP_NAME" --resource-group "$TF_RESOURCE_GROUP" --query "allowedOrigins" -o tsv 2>/dev/null || echo "")

        if [[ -n "$cors_config" ]]; then
            print_status "PASS" "CORS configuration found for backend"

            # Check if frontend URL is in CORS origins
            if [[ -n "$TF_FRONTEND_APP_NAME" ]]; then
                local frontend_url="https://${TF_FRONTEND_APP_NAME}.azurewebsites.net"
                if echo "$cors_config" | grep -q "$frontend_url"; then
                    print_status "PASS" "Frontend URL configured in backend CORS"
                else
                    print_status "WARN" "Frontend URL may not be configured in backend CORS"
                fi
            fi

            # Check for localhost (development)
            if echo "$cors_config" | grep -q "localhost"; then
                print_status "INFO" "Localhost configured in CORS (development access enabled)"
            fi
        else
            print_status "WARN" "No CORS configuration found for backend"
        fi
    fi

    # Check App Service Plan
    if [[ -n "$TF_RESOURCE_GROUP" ]]; then
        local app_plan=$(az appservice plan list --resource-group "$TF_RESOURCE_GROUP" --query "[0].{name:name, sku:sku.name, tier:sku.tier}" -o json 2>/dev/null || echo "[]")

        if [[ "$app_plan" != "[]" ]]; then
            local plan_name=$(echo "$app_plan" | jq -r '.[0].name')
            local plan_sku=$(echo "$app_plan" | jq -r '.[0].sku')
            local plan_tier=$(echo "$app_plan" | jq -r '.[0].tier')
            print_status "PASS" "App Service Plan exists" "$plan_name ($plan_tier, $plan_sku)"
        else
            print_status "WARN" "No App Service Plan found"
        fi
    fi
}

check_application_logs() {
    print_section "📋 Application Logs Analysis"

    if [[ -n "$TF_BACKEND_APP_NAME" ]] && [[ -n "$TF_RESOURCE_GROUP" ]]; then
        print_status "INFO" "Fetching recent application logs for backend"

        # Check if logging is enabled
        local log_config=$(az webapp log config show --name "$TF_BACKEND_APP_NAME" --resource-group "$TF_RESOURCE_GROUP" --query "applicationLogs.fileSystem.level" -o tsv 2>/dev/null || echo "")

        if [[ -n "$log_config" ]] && [[ "$log_config" != "Off" ]]; then
            print_status "PASS" "Application logging is enabled" "Level: $log_config"

            # Try to get recent logs (this may require additional permissions)
            local logs=$(timeout 10s az webapp log tail --name "$TF_BACKEND_APP_NAME" --resource-group "$TF_RESOURCE_GROUP" --provider application --max-events 10 2>/dev/null || echo "Could not fetch logs")

            if [[ "$logs" != "Could not fetch logs" ]]; then
                print_status "PASS" "Successfully retrieved application logs"

                # Check for specific patterns
                if echo "$logs" | grep -qi "error\|exception\|fail"; then
                    print_status "WARN" "Errors detected in recent logs"
                fi

                if echo "$logs" | grep -qi "openai\|assistant"; then
                    print_status "INFO" "OpenAI integration activity detected in logs"
                fi
            else
                print_status "WARN" "Could not fetch application logs" "May require additional permissions"
            fi
        else
            print_status "WARN" "Application logging is disabled"
        fi
    fi
}

generate_status_report() {
    print_section "📊 Deployment Status Report"

    # Calculate success rate
    local success_rate=0
    if [[ $TOTAL_CHECKS -gt 0 ]]; then
        success_rate=$(( (PASSED_CHECKS * 100) / TOTAL_CHECKS ))
    fi

    print_status "INFO" "Verification Summary"
    echo "      Total Checks: $TOTAL_CHECKS"
    echo "      Passed: $PASSED_CHECKS"
    echo "      Failed: $FAILED_CHECKS"
    echo "      Warnings: $WARNING_CHECKS"
    echo "      Success Rate: ${success_rate}%"

    # Resource Summary
    print_status "INFO" "Resource Summary"
    echo "      Environment: $ENVIRONMENT"
    echo "      Resource Group: ${TF_RESOURCE_GROUP:-'Not found'}"
    echo "      Container Registry: ${TF_ACR_NAME:-'Not found'}"
    echo "      Backend App: ${TF_BACKEND_APP_NAME:-'Not found'}"
    echo "      Frontend App: ${TF_FRONTEND_APP_NAME:-'Not found'}"
    echo "      Key Vault: ${TF_KEY_VAULT_NAME:-'Not found'}"
    echo "      Storage Account: ${TF_STORAGE_ACCOUNT_NAME:-'Not found'}"

    # URLs
    if [[ -n "$TF_BACKEND_APP_NAME" ]]; then
        echo "      Backend URL: https://${TF_BACKEND_APP_NAME}.azurewebsites.net"
    fi
    if [[ -n "$TF_FRONTEND_APP_NAME" ]]; then
        echo "      Frontend URL: https://${TF_FRONTEND_APP_NAME}.azurewebsites.net"
    fi

    # Recommendations
    print_status "INFO" "Recommendations"

    if [[ $FAILED_CHECKS -gt 0 ]]; then
        echo "      ❌ Critical issues found - deployment may not be functional"
        echo "      🔧 Run: terraform apply -var-file=environments/${ENVIRONMENT}.tfvars"
    fi

    if [[ $WARNING_CHECKS -gt 0 ]]; then
        echo "      ⚠️  Warnings found - some features may not work correctly"
        if [[ -n "$TF_KEY_VAULT_NAME" ]]; then
            echo "      🔑 Update secrets: ./scripts/manage-secrets.sh update"
        fi
    fi

    if [[ $FAILED_CHECKS -eq 0 ]] && [[ $WARNING_CHECKS -eq 0 ]]; then
        echo "      ✅ Deployment appears to be fully functional"
        echo "      🚀 Ready for application deployment via GitHub Actions"
    fi

    # Log file location
    print_status "INFO" "Detailed log saved to: $LOG_FILE"
}

show_troubleshooting_guide() {
    print_section "🔧 Troubleshooting Guide"

    echo "If you're experiencing issues, try these steps:"
    echo
    echo "1. 🏗️  Infrastructure Issues:"
    echo "   terraform plan -var-file=environments/${ENVIRONMENT}.tfvars"
    echo "   terraform apply -var-file=environments/${ENVIRONMENT}.tfvars"
    echo
    echo "2. 🔐 Secret Management:"
    echo "   ./scripts/manage-secrets.sh update"
    echo
    echo "3. 🔄 Restart Applications:"
    if [[ -n "$TF_BACKEND_APP_NAME" ]] && [[ -n "$TF_RESOURCE_GROUP" ]]; then
        echo "   az webapp restart --name $TF_BACKEND_APP_NAME --resource-group $TF_RESOURCE_GROUP"
    fi
    if [[ -n "$TF_FRONTEND_APP_NAME" ]] && [[ -n "$TF_RESOURCE_GROUP" ]]; then
        echo "   az webapp restart --name $TF_FRONTEND_APP_NAME --resource-group $TF_RESOURCE_GROUP"
    fi
    echo
    echo "4. 📋 Check Logs:"
    if [[ -n "$TF_BACKEND_APP_NAME" ]] && [[ -n "$TF_RESOURCE_GROUP" ]]; then
        echo "   az webapp log tail --name $TF_BACKEND_APP_NAME --resource-group $TF_RESOURCE_GROUP"
    fi
    echo
    echo "5. 🐳 Container Deployment:"
    echo "   # Trigger GitHub Actions workflow or manual deployment"
    echo "   gh workflow run filtro-curricular-applications.yml --field environment=$ENVIRONMENT"
    echo
    echo "6. 🔍 Manual Testing:"
    if [[ -n "$TF_BACKEND_APP_NAME" ]]; then
        echo "   curl -v https://${TF_BACKEND_APP_NAME}.azurewebsites.net/status"
        echo "   curl -v https://${TF_BACKEND_APP_NAME}.azurewebsites.net/health"
    fi
    echo
    echo "7. 📊 Monitoring:"
    echo "   # Check Application Insights in Azure Portal"
    echo "   # Resource Group: ${TF_RESOURCE_GROUP:-'filtro-curricular-rg-dev'}"
}

# Main verification function
main() {
    print_header "🚀 Filtro Curricular - Comprehensive Deployment Verification"
    print_status "INFO" "Starting verification for environment: $ENVIRONMENT"
    print_status "INFO" "Log file: $LOG_FILE"

    # Initialize status tracking
    TOTAL_CHECKS=0
    PASSED_CHECKS=0
    FAILED_CHECKS=0
    WARNING_CHECKS=0

    # Run verification steps
    local verification_failed=false

    # Prerequisites check
    if ! check_prerequisites; then
        verification_failed=true
    fi

    # Parse Terraform configuration
    if ! parse_terraform_config; then
        verification_failed=true
    fi

    # Analyze GitHub Actions workflows
    analyze_github_workflows

    # Verify Azure resources
    verify_azure_resources

    # Test health endpoints
    test_health_endpoints

    # Verify networking and CORS
    verify_networking_and_cors

    # Check application logs
    check_application_logs

    # Generate status report
    generate_status_report

    # Final summary
    print_header "🎯 Final Summary"

    if [[ $FAILED_CHECKS -eq 0 ]]; then
        if [[ $WARNING_CHECKS -eq 0 ]]; then
            print_status "PASS" "🎉 All verifications passed! Deployment is fully functional."
            echo -e "\n${GREEN}✅ Your Filtro Curricular deployment is ready for use!${NC}"
        else
            print_status "WARN" "⚠️  Deployment is functional but has warnings."
            echo -e "\n${YELLOW}⚠️  Some issues found - see recommendations above.${NC}"
        fi
    else
        print_status "FAIL" "❌ Critical issues found in deployment."
        echo -e "\n${RED}❌ Deployment has critical issues that need attention.${NC}"
        echo
        show_troubleshooting_guide
        verification_failed=true
    fi

    # Exit with appropriate code
    if [[ "$verification_failed" == "true" ]]; then
        exit 1
    else
        exit 0
    fi
}

# Quick verification functions for specific components
quick_secrets_check() {
    print_header "🔑 Quick Secrets Check"
    check_prerequisites
    parse_terraform_config
    verify_key_vault
}

quick_health_check() {
    print_header "🏥 Quick Health Check"
    check_prerequisites
    parse_terraform_config
    test_health_endpoints
}

quick_logs_check() {
    print_header "📋 Quick Logs Check"
    check_prerequisites
    parse_terraform_config
    check_application_logs
}

show_help() {
    echo "Filtro Curricular - Comprehensive Deployment Verification Script"
    echo
    echo "Usage: $0 [ENVIRONMENT] [COMMAND]"
    echo
    echo "ENVIRONMENT:"
    echo "  dev       Verify DEV environment (default)"
    echo "  staging   Verify STAGING environment"
    echo "  uat       Verify UAT environment"
    echo "  prod      Verify PROD environment"
    echo
    echo "COMMANDS:"
    echo "  (no command)  Run full comprehensive verification"
    echo "  secrets       Quick check of Key Vault secrets only"
    echo "  health        Quick check of health endpoints only"
    echo "  logs          Quick check of application logs only"
    echo "  troubleshoot  Show troubleshooting guide"
    echo "  help          Show this help message"
    echo
    echo "Examples:"
    echo "  $0                    # Full verification of DEV environment"
    echo "  $0 staging            # Full verification of STAGING environment"
    echo "  $0 dev secrets        # Quick secrets check for DEV"
    echo "  $0 prod health        # Quick health check for PROD"
    echo
    echo "Output:"
    echo "  - Console output with colored status indicators"
    echo "  - Detailed log file saved to scripts/ directory"
    echo "  - Exit code 0 for success, 1 for failures"
}

# =============================================================================
# COMMAND LINE ARGUMENT PARSING
# =============================================================================

# Parse arguments
ENVIRONMENT_ARG=""
COMMAND_ARG=""

# Check if first argument is an environment
case "${1:-}" in
    "dev"|"staging"|"uat"|"prod")
        ENVIRONMENT_ARG="$1"
        COMMAND_ARG="${2:-}"
        ;;
    "secrets"|"health"|"logs"|"troubleshoot"|"help"|"--help"|"-h")
        COMMAND_ARG="$1"
        ;;
    "")
        # No arguments - use defaults
        ;;
    *)
        echo -e "${RED}Error: Unknown argument '$1'${NC}"
        echo "Run '$0 help' for usage information"
        exit 1
        ;;
esac

# Set environment (use argument or default)
if [[ -n "$ENVIRONMENT_ARG" ]]; then
    ENVIRONMENT="$ENVIRONMENT_ARG"
fi

# Update tfvars file path based on environment
TFVARS_FILE="${TERRAFORM_DIR}/environments/${ENVIRONMENT}.tfvars"

# Execute based on command
case "$COMMAND_ARG" in
    "secrets")
        quick_secrets_check
        ;;
    "health")
        quick_health_check
        ;;
    "logs")
        quick_logs_check
        ;;
    "troubleshoot")
        check_prerequisites
        parse_terraform_config
        show_troubleshooting_guide
        ;;
    "help"|"--help"|"-h")
        show_help
        ;;
    "")
        main
        ;;
    *)
        echo -e "${RED}Error: Unknown command '$COMMAND_ARG'${NC}"
        echo "Run '$0 help' for usage information"
        exit 1
        ;;
esac
