variable "project_name" {
  description = "Name of the project"
  type        = string
  default     = "filtro-curricular"
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
  default     = "dev"
}

variable "location" {
  description = "Azure region for resources"
  type        = string
  default     = "brazilsouth"  # Changed from chilecentral to brazilsouth for full service support

  validation {
    condition = contains([
      "brazilsouth", "eastus", "eastus2", "westus", "westus2", "westus3",
      "centralus", "northcentralus", "southcentralus", "westcentralus",
      "canadacentral", "canadaeast", "chilecentral", "northeurope",
      "westeurope", "uksouth", "ukwest", "francecentral", "germanywestcentral",
      "norwayeast", "switzerlandnorth", "swedencentral", "eastasia",
      "southeastasia", "japaneast", "japanwest", "koreacentral",
      "australiaeast", "australiasoutheast", "centralindia", "southindia"
    ], var.location)
    error_message = "The location must be a valid Azure region name. Recommended: 'brazilsouth' for optimal Chile performance with full service support."
  }
}

variable "monitoring_location" {
  description = "Azure region for monitoring resources (Log Analytics, Application Insights)"
  type        = string
  default     = "eastus"

  validation {
    condition = contains([
      "eastus", "westeurope", "southeastasia", "australiasoutheast", "westcentralus",
      "japaneast", "uksouth", "centralindia", "canadacentral", "westus2",
      "australiacentral", "australiaeast", "francecentral", "koreacentral",
      "northeurope", "centralus", "eastasia", "eastus2", "southcentralus",
      "northcentralus", "westus", "ukwest", "southafricanorth", "brazilsouth",
      "switzerlandnorth", "switzerlandwest", "germanywestcentral", "australiacentral2",
      "uaecentral", "uaenorth", "japanwest", "brazilsoutheast", "norwayeast",
      "norwaywest", "francesouth", "southindia", "koreasouth", "jioindiacentral",
      "jioindiawest", "qatarcentral", "canadaeast", "westus3", "swedencentral",
      "southafricawest", "germanynorth", "polandcentral", "israelcentral",
      "italynorth", "mexicocentral", "spaincentral", "newzealandnorth"
    ], var.monitoring_location)
    error_message = "The monitoring_location must be a valid Azure region that supports Log Analytics Workspace."
  }
}

variable "tags" {
  description = "Tags to apply to all resources"
  type        = map(string)
  default = {
    Project     = "filtro-curricular"
    Environment = "dev"
    ManagedBy   = "terraform"
  }
}

# Container Registry variables
variable "acr_sku" {
  description = "SKU for Azure Container Registry"
  type        = string
  default     = "Basic"
}

# App Service Plan variables
variable "app_service_plan_sku" {
  description = "SKU for App Service Plan"
  type        = string
  default     = "B1"
}

# Backend application variables
variable "backend_image_tag" {
  description = "Docker image tag for backend"
  type        = string
  default     = "latest"
}

variable "backend_port" {
  description = "Port for backend application"
  type        = number
  default     = 5000
}

# Frontend application variables
variable "frontend_image_tag" {
  description = "Docker image tag for frontend"
  type        = string
  default     = "latest"
}

variable "frontend_port" {
  description = "Port for frontend application"
  type        = number
  default     = 80
}

# OpenAI Configuration
variable "openai_api_key" {
  description = "OpenAI API Key"
  type        = string
  sensitive   = true
}

variable "openai_token" {
  description = "OpenAI Token"
  type        = string
  sensitive   = true
}

variable "assistant_id_juridico" {
  description = "OpenAI Assistant ID for Juridico"
  type        = string
  sensitive   = true
}

variable "assistant_id_calidad" {
  description = "OpenAI Assistant ID for Calidad"
  type        = string
  sensitive   = true
}

# Storage Account variables
variable "storage_account_tier" {
  description = "Storage account tier"
  type        = string
  default     = "Standard"
}

variable "storage_account_replication_type" {
  description = "Storage account replication type"
  type        = string
  default     = "LRS"
}

# Custom domain variables (optional)
variable "custom_domain" {
  description = "Custom domain for the application"
  type        = string
  default     = ""
}

variable "ssl_certificate_path" {
  description = "Path to SSL certificate for custom domain"
  type        = string
  default     = ""
}

# Scaling variables
variable "backend_min_instances" {
  description = "Minimum number of backend instances"
  type        = number
  default     = 1
}

variable "backend_max_instances" {
  description = "Maximum number of backend instances"
  type        = number
  default     = 3
}

variable "frontend_min_instances" {
  description = "Minimum number of frontend instances"
  type        = number
  default     = 1
}

variable "frontend_max_instances" {
  description = "Maximum number of frontend instances"
  type        = number
  default     = 3
}

# Networking variables
variable "enable_vnet" {
  description = "Enable Virtual Network integration"
  type        = bool
  default     = false
}

variable "enable_app_gateway" {
  description = "Enable Application Gateway"
  type        = bool
  default     = false
}
