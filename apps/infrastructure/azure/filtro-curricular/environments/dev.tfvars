# Development Environment Configuration
project_name = "filtro-curricular"
environment  = "dev"
location     = "brazilsouth"

# Monitoring Configuration
monitoring_location = "eastus"

# Tags
tags = {
  Project     = "filtro-curricular"
  Environment = "dev"
  ManagedBy   = "terraform"
  Owner       = "Cognisearch"
  CostCenter  = "development"
}

# Azure Container Registry
acr_sku = "Basic"

# App Service Plan - Lower tier for development
app_service_plan_sku = "B1"

# Application Configuration
backend_image_tag  = "dev-latest"
frontend_image_tag = "dev-latest"
backend_port       = 5000
frontend_port      = 80

# Storage Account
storage_account_tier             = "Standard"
storage_account_replication_type = "LRS"

# Scaling Configuration - Minimal for dev
backend_min_instances  = 1
backend_max_instances  = 2
frontend_min_instances = 1
frontend_max_instances = 2

# Networking - Disabled for dev to reduce costs
enable_vnet        = false
enable_app_gateway = false

# Security - Relaxed for development
key_vault_network_access = "Allow"
enable_diagnostic_logs   = false

# Custom Domain - Not used in dev
custom_domain        = ""
ssl_certificate_path = ""

# Environment-specific secrets (will be set via GitHub secrets)
# These are placeholders - actual values come from GitHub environment secrets
openai_api_key        = "placeholder-will-be-set-by-github"
openai_token          = "placeholder-will-be-set-by-github"
assistant_id_juridico = "placeholder-will-be-set-by-github"
assistant_id_calidad  = "placeholder-will-be-set-by-github"
