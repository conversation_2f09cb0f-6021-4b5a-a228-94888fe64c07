# Production Environment Configuration
project_name = "filtro-curricular"
environment  = "prod"
location     = "brazilsouth"  # Migrated from chilecentral for full Azure service support

# Monitoring Configuration
monitoring_location = "eastus"

# Tags
tags = {
  Project     = "filtro-curricular"
  Environment = "prod"
  ManagedBy   = "terraform"
  Owner       = "Cognisearch"
  CostCenter  = "production"
  Compliance  = "required"
}

# Azure Container Registry
acr_sku = "Premium"

# App Service Plan - High-tier for production
app_service_plan_sku = "P1v3"

# Application Configuration
backend_image_tag  = "prod-latest"
frontend_image_tag = "prod-latest"
backend_port       = 5000
frontend_port      = 80

# Storage Account
storage_account_tier             = "Premium"
storage_account_replication_type = "GRS"

# Scaling Configuration - Full scaling for production
backend_min_instances  = 2
backend_max_instances  = 10
frontend_min_instances = 2
frontend_max_instances = 10

# Networking - Full security for production
enable_vnet        = true
enable_app_gateway = true

# Security - Maximum security for production
key_vault_network_access = "Deny"
enable_diagnostic_logs   = true

# Custom Domain - Production domain
custom_domain        = "filtro-curricular.yourdomain.com"
ssl_certificate_path = ""

# Environment-specific secrets (will be set via GitHub secrets)
# These are placeholders - actual values come from GitHub environment secrets
openai_api_key        = "placeholder-will-be-set-by-github"
openai_token          = "placeholder-will-be-set-by-github"
assistant_id_juridico = "placeholder-will-be-set-by-github"
assistant_id_calidad  = "placeholder-will-be-set-by-github"
