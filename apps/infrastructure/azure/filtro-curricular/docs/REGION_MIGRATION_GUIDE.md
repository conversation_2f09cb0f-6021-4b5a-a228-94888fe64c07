# Azure Region Migration Guide: Chile Central → Brazil South

## 🎯 **Migration Overview**

This guide covers the migration from `chilecentral` to `brazilsouth` region to resolve Azure service limitations while maintaining optimal performance for Chilean users.

### **Why Migrate?**
- **Service Limitations**: Chile Central doesn't support ACR Task Runs API
- **Full Service Portfolio**: Brazil South supports all required Azure services
- **Optimal Performance**: Only ~40-60ms latency increase from Santiago
- **Regional Compliance**: Maintains South American data residency

## 📊 **Performance Comparison**

| Metric | Chile Central | Brazil South | Impact |
|--------|---------------|--------------|---------|
| Latency from Santiago | ~5-15ms | ~40-60ms | +35-45ms |
| ACR Task Runs API | ❌ Not Supported | ✅ Supported | Fixed |
| All Azure Services | ❌ Limited | ✅ Full Portfolio | Complete |
| Data Residency | Chile | Brazil (SA) | Compliant |

## 🚀 **Migration Steps**

### **Phase 1: Pre-Migration Preparation**

1. **Backup Current State**
   ```bash
   cd apps/infrastructure/azure/filtro-curricular
   
   # Export current Terraform state
   terraform state pull > backup-state-$(date +%Y%m%d).json
   
   # Backup current resource configurations
   az group export --name filtro-curricular-rg-dev --include-comments > backup-resources-$(date +%Y%m%d).json
   ```

2. **Verify New Region Capabilities**
   ```bash
   # Test Brazil South region access
   az account list-locations --query "[?name=='brazilsouth']" --output table
   
   # Verify service availability
   az provider show --namespace Microsoft.ContainerRegistry --query "resourceTypes[?resourceType=='registries'].locations" --output table
   ```

### **Phase 2: Infrastructure Migration**

1. **Update Terraform Configuration** ✅ COMPLETED
   - Updated `variables.tf` default region to `brazilsouth`
   - Updated all environment `.tfvars` files
   - Updated validation rules and documentation

2. **Plan Migration**
   ```bash
   cd apps/infrastructure/azure/filtro-curricular
   
   # Initialize with new region
   terraform init
   
   # Plan the migration (will show resource recreation)
   terraform plan -var-file="terraform.tfvars"
   ```

3. **Execute Migration**
   ```bash
   # Apply changes (this will recreate resources in new region)
   terraform apply -var-file="terraform.tfvars"
   ```

### **Phase 3: Application Deployment**

1. **Update GitHub Actions Secrets** (if needed)
   - Verify Azure credentials work with Brazil South
   - Update any region-specific configurations

2. **Redeploy Applications**
   ```bash
   # Trigger application redeployment
   git add .
   git commit -m "migrate: Move infrastructure to Brazil South region"
   git push origin main
   ```

3. **Verify Deployment**
   ```bash
   # Check new ACR in Brazil South
   az acr list --resource-group filtro-curricular-rg-dev --output table
   
   # Test ACR Task Runs API (previously failing)
   az acr task list-runs --registry <NEW_ACR_NAME> --output table
   
   # Verify App Services
   az webapp list --resource-group filtro-curricular-rg-dev --output table
   ```

## 🔧 **Updated Monitoring Commands**

### **Container Registry (Now Working!)**
```bash
# ACR name will change due to region migration
NEW_ACR_NAME=$(terraform output -raw container_registry_name)

# Login to new ACR
az acr login --name $NEW_ACR_NAME

# List repositories
az acr repository list --name $NEW_ACR_NAME --output table

# Check task runs (now supported!)
az acr task list-runs --registry $NEW_ACR_NAME --output table

# Show image tags with timestamps
az acr repository show-tags --name $NEW_ACR_NAME --repository filtro-curricular-fe-web --output table --orderby time_desc
```

### **App Services**
```bash
# Get new resource names
RESOURCE_GROUP=$(terraform output -raw resource_group_name)
FRONTEND_APP=$(terraform output -raw frontend_app_name)
BACKEND_APP=$(terraform output -raw backend_app_name)

# Monitor deployments
az webapp deployment list --name $FRONTEND_APP --resource-group $RESOURCE_GROUP --output table
az webapp deployment list --name $BACKEND_APP --resource-group $RESOURCE_GROUP --output table

# Stream logs
az webapp log tail --name $FRONTEND_APP --resource-group $RESOURCE_GROUP
az webapp log tail --name $BACKEND_APP --resource-group $RESOURCE_GROUP
```

## ⚠️ **Important Considerations**

### **Data Migration**
- **Storage Accounts**: Data will need to be migrated manually if critical
- **Key Vault Secrets**: Will be recreated in new region
- **Application Insights**: Historical data will remain in old region

### **DNS and Networking**
- **App Service URLs**: Will change to Brazil South format
- **Custom Domains**: May need DNS updates if using custom domains
- **Network Latency**: Expect 35-45ms additional latency for end users

### **Cost Implications**
- **Brazil South Pricing**: Generally similar to Chile Central
- **Data Transfer**: Minimal impact for South American users
- **Migration Costs**: One-time resource recreation costs

## 🎯 **Post-Migration Validation**

### **Functional Testing**
```bash
# Test frontend application
curl -I https://<NEW_FRONTEND_URL>

# Test backend API
curl -I https://<NEW_BACKEND_URL>/status

# Test container registry
docker pull <NEW_ACR_LOGIN_SERVER>/filtro-curricular-fe-web:latest
```

### **Performance Testing**
```bash
# Test latency from Chile
ping <NEW_FRONTEND_URL>

# Load test (if applicable)
# Use your preferred load testing tool
```

## 🔄 **Rollback Plan**

If issues arise, you can rollback by:

1. **Revert Terraform Configuration**
   ```bash
   git revert <migration-commit-hash>
   ```

2. **Restore from Backup**
   ```bash
   terraform state push backup-state-<date>.json
   ```

3. **Redeploy to Original Region**
   ```bash
   terraform apply -var="location=chilecentral"
   ```

## 📞 **Support and Troubleshooting**

### **Common Issues**
- **Service Quotas**: Brazil South may have different quotas
- **Resource Naming**: Some resources may need different names
- **Network Connectivity**: Test connectivity from your CI/CD environment

### **Monitoring**
- Set up alerts for increased latency
- Monitor application performance post-migration
- Track user experience metrics

---

**Migration Status**: ✅ Terraform configurations updated, ready for execution
**Next Steps**: Execute Phase 2 (Infrastructure Migration) when ready
