# Updated Monitoring Commands for Brazil South Region

## 🎯 **Overview**

After migrating from Chile Central to Brazil South, all Azure services are now fully supported, including the ACR Task Runs API that was previously failing.

## 🔧 **Updated Resource Names**

Your resource names will change due to the region migration. Use these commands to get the new names:

```bash
cd apps/infrastructure/azure/filtro-curricular

# Get all resource names from Terraform
terraform output

# Get specific resource names
RESOURCE_GROUP=$(terraform output -raw resource_group_name)
ACR_NAME=$(terraform output -raw container_registry_name)
FRONTEND_APP=$(terraform output -raw frontend_app_name)
BACKEND_APP=$(terraform output -raw backend_app_name)

echo "Resource Group: $RESOURCE_GROUP"
echo "Container Registry: $ACR_NAME"
echo "Frontend App: $FRONTEND_APP"
echo "Backend App: $BACKEND_APP"
```

## 📦 **Azure Container Registry Monitoring (Now Fully Working!)**

### **Basic ACR Commands**
```bash
# Login to ACR
az acr login --name $ACR_NAME

# List all repositories
az acr repository list --name $ACR_NAME --output table

# Show tags for frontend image (sorted by date)
az acr repository show-tags --name $ACR_NAME --repository filtro-curricular-fe-web --output table --orderby time_desc

# Show tags for backend image (sorted by date)
az acr repository show-tags --name $ACR_NAME --repository filtro-curricular-be-api --output table --orderby time_desc
```

### **ACR Task Runs (Previously Failing - Now Working!)**
```bash
# List task runs (this was failing in Chile Central)
az acr task list-runs --registry $ACR_NAME --output table

# Get detailed task run information
az acr task list-runs --registry $ACR_NAME --output json

# Show specific task run details
az acr task show-run --registry $ACR_NAME --run-id <RUN_ID>
```

### **ACR Repository Details**
```bash
# Get repository details with metadata
az acr repository show --name $ACR_NAME --repository filtro-curricular-fe-web

# Get image manifest information
az acr repository show-manifests --name $ACR_NAME --repository filtro-curricular-fe-web --output table

# Check repository size and usage
az acr repository show --name $ACR_NAME --repository filtro-curricular-fe-web --query "{name:name, tagCount:tagCount, lastUpdateTime:lastUpdateTime}"
```

## 🌐 **App Service Monitoring**

### **Deployment Status**
```bash
# Check deployment history for frontend
az webapp deployment list --name $FRONTEND_APP --resource-group $RESOURCE_GROUP --output table

# Check deployment history for backend
az webapp deployment list --name $BACKEND_APP --resource-group $RESOURCE_GROUP --output table

# Get current deployment status
az webapp deployment list --name $FRONTEND_APP --resource-group $RESOURCE_GROUP --query "[0].{status:status, author:author, deploymentTime:receivedTime}"
```

### **App Service Status and Configuration**
```bash
# Check app service status
az webapp show --name $FRONTEND_APP --resource-group $RESOURCE_GROUP --query "{name:name, state:state, defaultHostName:defaultHostName, location:location}"

# Get container configuration
az webapp config container show --name $FRONTEND_APP --resource-group $RESOURCE_GROUP

# Check app settings (environment variables)
az webapp config appsettings list --name $FRONTEND_APP --resource-group $RESOURCE_GROUP --output table
```

### **Container Logs and Troubleshooting**
```bash
# Stream logs in real-time (frontend)
az webapp log tail --name $FRONTEND_APP --resource-group $RESOURCE_GROUP

# Stream logs in real-time (backend)
az webapp log tail --name $BACKEND_APP --resource-group $RESOURCE_GROUP

# Download log files
az webapp log download --name $FRONTEND_APP --resource-group $RESOURCE_GROUP --log-file frontend-logs.zip
az webapp log download --name $BACKEND_APP --resource-group $RESOURCE_GROUP --log-file backend-logs.zip

# Get container startup logs
az webapp log show --name $BACKEND_APP --resource-group $RESOURCE_GROUP
```

### **App Service Management**
```bash
# Restart services
az webapp restart --name $FRONTEND_APP --resource-group $RESOURCE_GROUP
az webapp restart --name $BACKEND_APP --resource-group $RESOURCE_GROUP

# Stop/Start services
az webapp stop --name $FRONTEND_APP --resource-group $RESOURCE_GROUP
az webapp start --name $FRONTEND_APP --resource-group $RESOURCE_GROUP

# Scale services (if needed)
az webapp scale --name $FRONTEND_APP --resource-group $RESOURCE_GROUP --instance-count 2
```

## 🔍 **GitHub Actions Monitoring**

### **Workflow Monitoring**
```bash
# List recent workflow runs
gh run list --repo jherrera-rgt/ragtech --workflow="filtro-curricular-applications.yml" --limit 10

# Watch the latest workflow run in real-time
gh run watch --repo jherrera-rgt/ragtech

# Get detailed logs for a specific run
gh run view <RUN_ID> --repo jherrera-rgt/ragtech --log

# List all workflows
gh workflow list --repo jherrera-rgt/ragtech
```

### **Workflow Status**
```bash
# Check workflow status
gh run list --repo jherrera-rgt/ragtech --status completed --limit 5
gh run list --repo jherrera-rgt/ragtech --status failure --limit 5

# Get workflow run details
gh run view --repo jherrera-rgt/ragtech --json status,conclusion,createdAt,updatedAt
```

## 🏥 **Health Checks and Testing**

### **Application Health**
```bash
# Test frontend health
curl -I https://$FRONTEND_APP.azurewebsites.net

# Test backend health (if health endpoint exists)
curl -I https://$BACKEND_APP.azurewebsites.net/status
curl -I https://$BACKEND_APP.azurewebsites.net/health

# Test with detailed response
curl -v https://$FRONTEND_APP.azurewebsites.net
```

### **Network and Performance Testing**
```bash
# Test latency from your location
ping $FRONTEND_APP.azurewebsites.net

# Test DNS resolution
nslookup $FRONTEND_APP.azurewebsites.net

# Test SSL certificate
openssl s_client -connect $FRONTEND_APP.azurewebsites.net:443 -servername $FRONTEND_APP.azurewebsites.net
```

## 📊 **Resource Usage and Monitoring**

### **Resource Group Overview**
```bash
# List all resources in the resource group
az resource list --resource-group $RESOURCE_GROUP --output table

# Get resource group details
az group show --name $RESOURCE_GROUP

# Check resource group location (should be brazilsouth)
az group show --name $RESOURCE_GROUP --query location
```

### **Cost and Usage Monitoring**
```bash
# Get resource usage (if cost management is enabled)
az consumption usage list --start-date 2025-06-01 --end-date 2025-06-17

# List resource costs by resource group
az consumption usage list --resource-group $RESOURCE_GROUP
```

## 🚨 **Troubleshooting Commands**

### **Common Issues**
```bash
# Check if resources are in the correct region
az resource list --resource-group $RESOURCE_GROUP --query "[].{name:name, type:type, location:location}" --output table

# Verify container registry login
az acr login --name $ACR_NAME --expose-token

# Check app service container settings
az webapp config container show --name $FRONTEND_APP --resource-group $RESOURCE_GROUP --query "{image:linuxFxVersion, registryUrl:dockerRegistryServerUrl}"

# Test container registry connectivity
docker pull $ACR_NAME.azurecr.io/filtro-curricular-fe-web:latest
```

### **Performance Issues**
```bash
# Check app service metrics
az monitor metrics list --resource "/subscriptions/$(az account show --query id -o tsv)/resourceGroups/$RESOURCE_GROUP/providers/Microsoft.Web/sites/$FRONTEND_APP" --metric "CpuPercentage,MemoryPercentage,HttpResponseTime"

# Check container registry metrics
az monitor metrics list --resource "/subscriptions/$(az account show --query id -o tsv)/resourceGroups/$RESOURCE_GROUP/providers/Microsoft.ContainerRegistry/registries/$ACR_NAME" --metric "TotalPullCount,TotalPushCount"
```

## 🔄 **Automated Monitoring Script**

Create a simple monitoring script:

```bash
#!/bin/bash
# monitoring-check.sh

# Get resource names
RESOURCE_GROUP=$(cd apps/infrastructure/azure/filtro-curricular && terraform output -raw resource_group_name)
ACR_NAME=$(cd apps/infrastructure/azure/filtro-curricular && terraform output -raw container_registry_name)
FRONTEND_APP=$(cd apps/infrastructure/azure/filtro-curricular && terraform output -raw frontend_app_name)
BACKEND_APP=$(cd apps/infrastructure/azure/filtro-curricular && terraform output -raw backend_app_name)

echo "=== Filtro Curricular Health Check ==="
echo "Resource Group: $RESOURCE_GROUP"
echo "Region: $(az group show --name $RESOURCE_GROUP --query location -o tsv)"
echo ""

echo "=== Container Registry Status ==="
az acr repository list --name $ACR_NAME --output table
echo ""

echo "=== App Services Status ==="
az webapp list --resource-group $RESOURCE_GROUP --query "[].{name:name, state:state, location:location}" --output table
echo ""

echo "=== Latest Deployments ==="
az webapp deployment list --name $FRONTEND_APP --resource-group $RESOURCE_GROUP --query "[0].{app:'frontend', status:status, time:receivedTime}" --output table
az webapp deployment list --name $BACKEND_APP --resource-group $RESOURCE_GROUP --query "[0].{app:'backend', status:status, time:receivedTime}" --output table
```

Save this as `monitoring-check.sh` and run it periodically to get a quick status overview.

---

**Note**: All these commands now work properly in Brazil South region, including the ACR Task Runs API that was failing in Chile Central! 🎉
