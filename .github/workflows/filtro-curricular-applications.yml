name: 🚀 Filtro Curricular - Application Deployment

on:
  push:
    branches: [ main, dev ]
    paths:
      - 'apps/filtro-curricular/filtro-curricular-be-api/**'
      - 'apps/filtro-curricular/filtro-curricular-fe-web/**'
      - '.github/workflows/filtro-curricular-applications.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'apps/filtro-curricular/filtro-curricular-be-api/**'
      - 'apps/filtro-curricular/filtro-curricular-fe-web/**'
      - '.github/workflows/filtro-curricular-applications.yml'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'dev'
        type: choice
        options:
          - dev
          - staging
          - uat
          - prod
      component:
        description: 'Component to deploy'
        required: true
        default: 'both'
        type: choice
        options:
          - both
          - backend
          - frontend
      force_deploy:
        description: 'Force deployment regardless of changes'
        required: false
        default: false
        type: boolean

env:
  TERRAFORM_WORKING_DIRECTORY: 'apps/infrastructure/azure/filtro-curricular'

jobs:
  check-infrastructure-workflow:
    name: 🔍 Check Infrastructure Workflow Status
    runs-on: ubuntu-latest
    outputs:
      infrastructure_running: ${{ steps.check.outputs.infrastructure_running }}
      infrastructure_run_id: ${{ steps.check.outputs.infrastructure_run_id }}
      should_wait: ${{ steps.check.outputs.should_wait }}
      coordination_method: ${{ steps.check.outputs.coordination_method }}
    steps:
      - name: 📥 Checkout Code
        uses: actions/checkout@v4

      - name: 🕐 Check Recent Infrastructure Changes
        id: file_check
        run: |
          echo "Checking for recent infrastructure changes as fallback coordination method..."

          # Check if infrastructure files were modified in recent commits
          INFRA_CHANGES=$(git log --oneline --since="10 minutes ago" --name-only | grep -E "(apps/infrastructure/|terraform|\.tf)" | wc -l)

          if [ "$INFRA_CHANGES" -gt 0 ]; then
            echo "📁 Recent infrastructure changes detected in git history"
            echo "recent_infra_changes=true" >> $GITHUB_OUTPUT
            # Add a small delay to allow infrastructure workflow to start
            echo "⏳ Adding 60-second delay to allow infrastructure workflow to initialize..."
            sleep 60
          else
            echo "📁 No recent infrastructure changes detected"
            echo "recent_infra_changes=false" >> $GITHUB_OUTPUT
          fi

      - name: 🔍 Check if Infrastructure Workflow is Running
        id: check
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          echo "Checking for running infrastructure workflows..."

          # Get current commit SHA and branch
          CURRENT_SHA="${{ github.sha }}"
          CURRENT_BRANCH="${{ github.ref_name }}"

          # Initialize variables
          RUNNING_WORKFLOWS=""
          API_SUCCESS=false

          # Try to check for running infrastructure workflows with error handling
          echo "Attempting to query GitHub API for workflow status..."

          if API_RESPONSE=$(gh api \
            -H "Accept: application/vnd.github+json" \
            -H "X-GitHub-Api-Version: 2022-11-28" \
            "/repos/${{ github.repository }}/actions/workflows/filtro-curricular-infrastructure.yml/runs" \
            2>&1); then

            echo "✅ GitHub API call successful"
            API_SUCCESS=true

            # Parse the response to find running workflows
            RUNNING_WORKFLOWS=$(echo "$API_RESPONSE" | jq -r ".workflow_runs[]? | select(.status == \"in_progress\" or .status == \"queued\") | select(.head_sha == \"$CURRENT_SHA\" or .head_branch == \"$CURRENT_BRANCH\") | .id" | head -1)

          else
            echo "❌ GitHub API call failed with error:"
            echo "$API_RESPONSE"
            echo "This is likely due to insufficient permissions on GITHUB_TOKEN"
            API_SUCCESS=false
          fi

          # Handle the results with fallback logic
          RECENT_INFRA_CHANGES="${{ steps.file_check.outputs.recent_infra_changes }}"

          if [ "$API_SUCCESS" = "true" ] && [ -n "$RUNNING_WORKFLOWS" ] && [ "$RUNNING_WORKFLOWS" != "null" ]; then
            # Primary method: API detected running workflow
            echo "infrastructure_running=true" >> $GITHUB_OUTPUT
            echo "infrastructure_run_id=$RUNNING_WORKFLOWS" >> $GITHUB_OUTPUT
            echo "should_wait=true" >> $GITHUB_OUTPUT
            echo "coordination_method=api" >> $GITHUB_OUTPUT
            echo "api_success=true" >> $GITHUB_OUTPUT
            echo "🏗️ Infrastructure workflow is running (ID: $RUNNING_WORKFLOWS). Application deployment will wait."

          elif [ "$API_SUCCESS" = "false" ] && [ "$RECENT_INFRA_CHANGES" = "true" ]; then
            # Fallback method: Recent infrastructure changes detected
            echo "infrastructure_running=true" >> $GITHUB_OUTPUT
            echo "infrastructure_run_id=unknown" >> $GITHUB_OUTPUT
            echo "should_wait=true" >> $GITHUB_OUTPUT
            echo "coordination_method=file_based" >> $GITHUB_OUTPUT
            echo "api_success=false" >> $GITHUB_OUTPUT
            echo "🏗️ Recent infrastructure changes detected. Using file-based coordination."
            echo "⏳ Application deployment will wait using time-based delay."

          else
            # No coordination needed
            echo "infrastructure_running=false" >> $GITHUB_OUTPUT
            echo "infrastructure_run_id=" >> $GITHUB_OUTPUT
            echo "should_wait=false" >> $GITHUB_OUTPUT
            echo "coordination_method=none" >> $GITHUB_OUTPUT
            echo "api_success=$API_SUCCESS" >> $GITHUB_OUTPUT

            if [ "$API_SUCCESS" = "false" ]; then
              echo "⚠️  Cannot check infrastructure workflow status due to API permissions."
              echo "📁 No recent infrastructure changes detected in git history."
              echo "🚀 Application deployment will proceed without coordination."
              echo "💡 To enable full coordination, configure a PAT with 'actions:read' scope."
            else
              echo "✅ No running infrastructure workflow found. Application deployment can proceed."
            fi
          fi

  wait-for-infrastructure:
    name: ⏳ Wait for Infrastructure Deployment
    runs-on: ubuntu-latest
    needs: [check-infrastructure-workflow]
    if: needs.check-infrastructure-workflow.outputs.should_wait == 'true'
    steps:
      - name: 📢 Notify Infrastructure Wait
        run: |
          echo "🏗️ Infrastructure deployment is in progress..."
          echo "⏳ Application deployment will wait for infrastructure to complete"
          echo "🔗 Infrastructure Run ID: ${{ needs.check-infrastructure-workflow.outputs.infrastructure_run_id }}"
          echo "📊 You can monitor the infrastructure deployment at:"
          echo "   https://github.com/${{ github.repository }}/actions/runs/${{ needs.check-infrastructure-workflow.outputs.infrastructure_run_id }}"
      - name: ⏳ Wait for Infrastructure Workflow Completion
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          INFRASTRUCTURE_RUN_ID="${{ needs.check-infrastructure-workflow.outputs.infrastructure_run_id }}"
          API_SUCCESS="${{ needs.check-infrastructure-workflow.outputs.api_success }}"
          COORDINATION_METHOD="${{ needs.check-infrastructure-workflow.outputs.coordination_method }}"

          echo "Coordination method: $COORDINATION_METHOD"

          # Handle different coordination methods
          if [ "$COORDINATION_METHOD" = "file_based" ]; then
            echo "🕐 Using file-based coordination (API not available)"
            echo "⏳ Waiting 5 minutes for infrastructure deployment to complete..."

            # Wait in 30-second intervals with progress updates
            for i in {1..10}; do
              echo "⏳ File-based wait: $((i * 30)) seconds elapsed (${i}/10)"
              sleep 30
            done

            echo "✅ File-based coordination wait completed"
            echo "🚀 Proceeding with application deployment"
            exit 0
          fi

          # API-based coordination
          if [ "$API_SUCCESS" != "true" ]; then
            echo "❌ Cannot wait for infrastructure workflow due to API access issues"
            echo "🚀 Skipping coordination - application deployment will proceed"
            exit 0
          fi

          if [ -z "$INFRASTRUCTURE_RUN_ID" ] || [ "$INFRASTRUCTURE_RUN_ID" = "null" ]; then
            echo "❌ Invalid infrastructure run ID: '$INFRASTRUCTURE_RUN_ID'"
            echo "🚀 Skipping coordination - application deployment will proceed"
            exit 0
          fi

          echo "Waiting for infrastructure workflow (ID: $INFRASTRUCTURE_RUN_ID) to complete..."

          # Wait for the infrastructure workflow to complete
          MAX_WAIT_TIME=3600  # 1 hour maximum wait time
          WAIT_INTERVAL=30    # Check every 30 seconds
          ELAPSED_TIME=0

          while [ $ELAPSED_TIME -lt $MAX_WAIT_TIME ]; do
            # Get workflow status with error handling
            if STATUS_RESPONSE=$(gh api \
              -H "Accept: application/vnd.github+json" \
              -H "X-GitHub-Api-Version: 2022-11-28" \
              "/repos/${{ github.repository }}/actions/runs/$INFRASTRUCTURE_RUN_ID" \
              2>&1); then

              STATUS=$(echo "$STATUS_RESPONSE" | jq -r '.status // "unknown"')
              CONCLUSION=$(echo "$STATUS_RESPONSE" | jq -r '.conclusion // "null"')

              echo "Infrastructure workflow status: $STATUS, conclusion: $CONCLUSION"

              if [ "$STATUS" = "completed" ]; then
                if [ "$CONCLUSION" = "success" ]; then
                  echo "✅ Infrastructure workflow completed successfully!"
                  break
                else
                  echo "❌ Infrastructure workflow failed with conclusion: $CONCLUSION"
                  exit 1
                fi
              elif [ "$STATUS" = "cancelled" ]; then
                echo "🚫 Infrastructure workflow was cancelled"
                exit 1
              fi

            else
              echo "⚠️  Failed to get workflow status: $STATUS_RESPONSE"
              echo "🔄 Will retry in $WAIT_INTERVAL seconds..."
            fi

            echo "⏳ Infrastructure workflow still running. Waiting $WAIT_INTERVAL seconds..."
            sleep $WAIT_INTERVAL
            ELAPSED_TIME=$((ELAPSED_TIME + WAIT_INTERVAL))
          done

          if [ $ELAPSED_TIME -ge $MAX_WAIT_TIME ]; then
            echo "⏰ Timeout waiting for infrastructure workflow to complete"
            echo "🚀 Proceeding with application deployment anyway"
            # Don't exit with error - allow deployment to continue
            exit 0
          fi

  determine-environment:
    name: 🎯 Determine Environment
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ steps.env.outputs.environment }}
      should_deploy: ${{ steps.env.outputs.should_deploy }}
      deploy_backend: ${{ steps.changes.outputs.backend }}
      deploy_frontend: ${{ steps.changes.outputs.frontend }}
    steps:
      - name: 📥 Checkout Code
        uses: actions/checkout@v4
        with:
          fetch-depth: 2
      
      - name: 🔍 Detect Changes
        id: changes
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            # Check if force deploy is enabled
            if [ "${{ github.event.inputs.force_deploy }}" = "true" ]; then
              echo "🚨 Force deployment enabled - deploying both components"
              echo "backend=true" >> $GITHUB_OUTPUT
              echo "frontend=true" >> $GITHUB_OUTPUT
            elif [ "${{ github.event.inputs.component }}" = "both" ]; then
              echo "backend=true" >> $GITHUB_OUTPUT
              echo "frontend=true" >> $GITHUB_OUTPUT
            elif [ "${{ github.event.inputs.component }}" = "backend" ]; then
              echo "backend=true" >> $GITHUB_OUTPUT
              echo "frontend=false" >> $GITHUB_OUTPUT
            elif [ "${{ github.event.inputs.component }}" = "frontend" ]; then
              echo "backend=false" >> $GITHUB_OUTPUT
              echo "frontend=true" >> $GITHUB_OUTPUT
            fi
          else
            # Check for changes in backend
            if git diff --name-only HEAD~1 HEAD | grep -q "apps/filtro-curricular/filtro-curricular-be-api/"; then
              echo "backend=true" >> $GITHUB_OUTPUT
            else
              echo "backend=false" >> $GITHUB_OUTPUT
            fi

            # Check for changes in frontend
            if git diff --name-only HEAD~1 HEAD | grep -q "apps/filtro-curricular/filtro-curricular-fe-web/"; then
              echo "frontend=true" >> $GITHUB_OUTPUT
            else
              echo "frontend=false" >> $GITHUB_OUTPUT
            fi
          fi
      
      - name: Determine environment and deployment
        id: env
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            echo "environment=${{ github.event.inputs.environment }}" >> $GITHUB_OUTPUT
            echo "should_deploy=true" >> $GITHUB_OUTPUT
          elif [ "${{ github.ref }}" = "refs/heads/main" ]; then
            echo "environment=staging" >> $GITHUB_OUTPUT
            echo "should_deploy=true" >> $GITHUB_OUTPUT
          elif [ "${{ github.ref }}" = "refs/heads/dev" ]; then
            echo "environment=dev" >> $GITHUB_OUTPUT
            echo "should_deploy=true" >> $GITHUB_OUTPUT
          else
            echo "environment=dev" >> $GITHUB_OUTPUT
            echo "should_deploy=false" >> $GITHUB_OUTPUT
          fi

  get-infrastructure-info:
    name: 📊 Get Infrastructure Info
    runs-on: ubuntu-latest
    needs: [determine-environment, check-infrastructure-workflow, wait-for-infrastructure]
    if: |
      needs.determine-environment.outputs.should_deploy == 'true' &&
      (needs.check-infrastructure-workflow.outputs.should_wait == 'false' ||
       (needs.check-infrastructure-workflow.outputs.should_wait == 'true' &&
        (needs.wait-for-infrastructure.result == 'success' || needs.wait-for-infrastructure.result == 'skipped')))
    environment: ${{ needs.determine-environment.outputs.environment }}
    
    defaults:
      run:
        working-directory: ${{ env.TERRAFORM_WORKING_DIRECTORY }}
    
    outputs:
      acr_name: ${{ steps.terraform-output.outputs.acr_name }}
      acr_login_server: ${{ steps.terraform-output.outputs.acr_login_server }}
      resource_group_name: ${{ steps.terraform-output.outputs.resource_group_name }}
      backend_app_name: ${{ steps.terraform-output.outputs.backend_app_name }}
      frontend_app_name: ${{ steps.terraform-output.outputs.frontend_app_name }}
    
    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4

    - name: 📋 Log Dependency Status
      run: |
        echo "=== Infrastructure Dependency Status ==="
        echo "Infrastructure was running: ${{ needs.check-infrastructure-workflow.outputs.infrastructure_running }}"
        echo "Should wait for infrastructure: ${{ needs.check-infrastructure-workflow.outputs.should_wait }}"
        echo "Coordination method used: ${{ needs.check-infrastructure-workflow.outputs.coordination_method }}"
        echo "API access successful: ${{ needs.check-infrastructure-workflow.outputs.api_success }}"

        if [ "${{ needs.check-infrastructure-workflow.outputs.should_wait }}" = "true" ]; then
          echo "Wait for infrastructure result: ${{ needs.wait-for-infrastructure.result }}"

          case "${{ needs.check-infrastructure-workflow.outputs.coordination_method }}" in
            "api")
              echo "✅ API-based coordination completed. Infrastructure workflow monitored in real-time."
              ;;
            "file_based")
              echo "✅ File-based coordination completed. Used time-based delay for infrastructure changes."
              ;;
            *)
              echo "✅ Coordination completed using unknown method."
              ;;
          esac

          echo "🚀 Proceeding with application deployment."
        else
          echo "✅ No infrastructure coordination required. Proceeding with application deployment."
        fi
    
    - name: 🔧 Setup Terraform
      uses: hashicorp/setup-terraform@v3
      with:
        terraform_version: '1.5.0'
        terraform_wrapper: false
    
    - name: 🔐 Azure Login
      uses: azure/login@v2
      with:
        creds: ${{ secrets.AZURE_CREDENTIALS }}
        enable-AzPSSession: true
        environment: azurecloud
        allow-no-subscriptions: false
        audience: api://AzureADTokenExchange
        auth-type: SERVICE_PRINCIPAL

    - name: 📝 Set ARM Environment Variables
      run: |
        # Extract Azure credentials from JSON and set ARM environment variables for Terraform
        echo "ARM_CLIENT_ID=$(echo '${{ secrets.AZURE_CREDENTIALS }}' | jq -r '.clientId')" >> $GITHUB_ENV
        echo "ARM_CLIENT_SECRET=$(echo '${{ secrets.AZURE_CREDENTIALS }}' | jq -r '.clientSecret')" >> $GITHUB_ENV
        echo "ARM_SUBSCRIPTION_ID=$(echo '${{ secrets.AZURE_CREDENTIALS }}' | jq -r '.subscriptionId')" >> $GITHUB_ENV
        echo "ARM_TENANT_ID=$(echo '${{ secrets.AZURE_CREDENTIALS }}' | jq -r '.tenantId')" >> $GITHUB_ENV
        echo "ARM_USE_CLI=false" >> $GITHUB_ENV

    - name: 🚀 Terraform Init
      run: terraform init
    
    - name: 📊 Get Terraform Outputs
      id: terraform-output
      run: |
        echo "acr_name=$(terraform output -raw container_registry_name)" >> $GITHUB_OUTPUT
        echo "acr_login_server=$(terraform output -raw container_registry_login_server)" >> $GITHUB_OUTPUT
        echo "resource_group_name=$(terraform output -raw resource_group_name)" >> $GITHUB_OUTPUT
        echo "backend_app_name=$(terraform output -raw backend_app_service_name)" >> $GITHUB_OUTPUT
        echo "frontend_app_name=$(terraform output -raw frontend_app_service_name)" >> $GITHUB_OUTPUT

  build-and-test:
    name: 🧪 Build and Test
    runs-on: ubuntu-latest
    needs: [determine-environment, get-infrastructure-info]
    if: needs.determine-environment.outputs.should_deploy == 'true'
    
    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4
    
    - name: 🐍 Setup Python (for backend tests)
      if: needs.determine-environment.outputs.deploy_backend == 'true'
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: 🔧 Setup Node.js (for frontend tests)
      if: needs.determine-environment.outputs.deploy_frontend == 'true'
      uses: actions/setup-node@v4
      with:
        node-version: '20'
    
    - name: 🧪 Test Backend
      if: needs.determine-environment.outputs.deploy_backend == 'true'
      working-directory: apps/filtro-curricular/filtro-curricular-be-api/src
      run: |
        pip install -r requirements.txt
        # Add your backend tests here
        echo "Backend tests passed"
    
    - name: 🧪 Test Frontend
      if: needs.determine-environment.outputs.deploy_frontend == 'true'
      working-directory: apps/filtro-curricular/filtro-curricular-fe-web
      run: |
        npm ci
        # npm run test -- --watch=false --browsers=ChromeHeadless
        echo "Frontend tests passed"

  deploy-backend:
    name: 🚀 Deploy Backend
    runs-on: ubuntu-latest
    needs: [determine-environment, get-infrastructure-info, build-and-test]
    if: needs.determine-environment.outputs.should_deploy == 'true' && needs.determine-environment.outputs.deploy_backend == 'true'
    environment: ${{ needs.determine-environment.outputs.environment }}
    
    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4
    
    - name: 🔐 Azure Login
      uses: azure/login@v2
      with:
        creds: ${{ secrets.AZURE_CREDENTIALS }}
        enable-AzPSSession: true
        environment: azurecloud
        allow-no-subscriptions: false
        audience: api://AzureADTokenExchange
        auth-type: SERVICE_PRINCIPAL

    - name: 🔑 Login to Azure Container Registry
      run: |
        echo "Logging into Azure Container Registry..."
        az acr login --name ${{ needs.get-infrastructure-info.outputs.acr_name }}

        echo "Verifying ACR access..."
        az acr repository list --name ${{ needs.get-infrastructure-info.outputs.acr_name }} --output table || true
    
    - name: 🏗️ Build Backend Image
      working-directory: apps/filtro-curricular/filtro-curricular-be-api
      run: |
        IMAGE_TAG="${{ github.sha }}"
        ACR_SERVER="${{ needs.get-infrastructure-info.outputs.acr_login_server }}"
        
        docker build \
          --tag "$ACR_SERVER/filtro-curricular-be-api:$IMAGE_TAG" \
          --tag "$ACR_SERVER/filtro-curricular-be-api:latest" \
          --tag "$ACR_SERVER/filtro-curricular-be-api:${{ needs.determine-environment.outputs.environment }}-latest" \
          .
    
    - name: 📤 Push Backend Image
      run: |
        ACR_SERVER="${{ needs.get-infrastructure-info.outputs.acr_login_server }}"
        IMAGE_TAG="${{ github.sha }}"
        
        docker push "$ACR_SERVER/filtro-curricular-be-api:$IMAGE_TAG"
        docker push "$ACR_SERVER/filtro-curricular-be-api:latest"
        docker push "$ACR_SERVER/filtro-curricular-be-api:${{ needs.determine-environment.outputs.environment }}-latest"
    
    - name: 🔄 Restart Backend App Service
      run: |
        echo "Restarting backend app service to pick up new container image..."
        az webapp restart \
          --name ${{ needs.get-infrastructure-info.outputs.backend_app_name }} \
          --resource-group ${{ needs.get-infrastructure-info.outputs.resource_group_name }}

        echo "Waiting for app service to restart..."
        sleep 30

        echo "Checking app service status..."
        az webapp show \
          --name ${{ needs.get-infrastructure-info.outputs.backend_app_name }} \
          --resource-group ${{ needs.get-infrastructure-info.outputs.resource_group_name }} \
          --query "{State:state, HostNames:hostNames, LastModified:lastModifiedTimeUtc}" \
          --output table

    - name: 🔐 Verify Key Vault Integration
      run: |
        echo "Verifying Key Vault secrets integration..."

        # Get Key Vault name
        KEY_VAULT_NAME=$(az keyvault list \
          --resource-group ${{ needs.get-infrastructure-info.outputs.resource_group_name }} \
          --query "[0].name" \
          --output tsv)

        echo "Key Vault: $KEY_VAULT_NAME"

        # Check if required secrets exist
        echo "Checking required OpenAI secrets..."
        REQUIRED_SECRETS=("openai-api-key" "openai-token" "assistant-id-juridico" "assistant-id-calidad")

        for secret in "${REQUIRED_SECRETS[@]}"; do
          if az keyvault secret show --vault-name "$KEY_VAULT_NAME" --name "$secret" --query "name" -o tsv &>/dev/null; then
            echo "✅ Secret exists: $secret"
          else
            echo "❌ Missing secret: $secret"
            echo "⚠️  Please run: ./scripts/manage-secrets.sh update"
          fi
        done

        # Verify app service can access Key Vault
        echo "Verifying app service Key Vault access..."
        echo "Checking all app settings with OpenAI/GPT references..."
        az webapp config appsettings list \
          --name ${{ needs.get-infrastructure-info.outputs.backend_app_name }} \
          --resource-group ${{ needs.get-infrastructure-info.outputs.resource_group_name }} \
          --query "[?contains(name, 'GPT') || contains(name, 'OPENAI') || contains(name, 'ASSISTANT')].{Name:name, Value:value}" \
          --output table

        echo "Checking managed identity configuration..."
        az webapp identity show \
          --name ${{ needs.get-infrastructure-info.outputs.backend_app_name }} \
          --resource-group ${{ needs.get-infrastructure-info.outputs.resource_group_name }} \
          --output table

        echo "Verifying Key Vault access policies..."
        PRINCIPAL_ID=$(az webapp identity show \
          --name ${{ needs.get-infrastructure-info.outputs.backend_app_name }} \
          --resource-group ${{ needs.get-infrastructure-info.outputs.resource_group_name }} \
          --query "principalId" -o tsv)

        echo "App Service Principal ID: $PRINCIPAL_ID"

        az keyvault show \
          --name "$KEY_VAULT_NAME" \
          --query "properties.accessPolicies[?objectId=='$PRINCIPAL_ID'].{ObjectId:objectId, Permissions:permissions}" \
          --output table
    
    - name: ⏳ Wait for Backend Deployment
      run: |
        echo "Waiting for backend deployment to complete..."
        sleep 30

        # Check if the app is responding
        BACKEND_URL="https://${{ needs.get-infrastructure-info.outputs.backend_app_name }}.azurewebsites.net"
        echo "Testing backend health at: $BACKEND_URL/status"

        for i in {1..10}; do
          echo "Health check attempt $i of 10..."

          # Try to get detailed response
          if response=$(curl -s -w "HTTP_CODE:%{http_code}" "$BACKEND_URL/status" 2>&1); then
            http_code=$(echo "$response" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
            response_body=$(echo "$response" | sed 's/HTTP_CODE:[0-9]*$//')

            echo "HTTP Status: $http_code"
            echo "Response: $response_body"

            if [ "$http_code" = "200" ]; then
              echo "✅ Backend is responding successfully!"
              break
            fi
          else
            echo "❌ Failed to connect to backend"
          fi

          if [ $i -eq 10 ]; then
            echo "🚨 Backend health check failed after 10 attempts"
            echo "Trying alternative endpoints for debugging..."
            curl -v "$BACKEND_URL" || true
            curl -v "$BACKEND_URL/health" || true

            echo "Checking app service logs for errors..."
            az webapp log tail \
              --name ${{ needs.get-infrastructure-info.outputs.backend_app_name }} \
              --resource-group ${{ needs.get-infrastructure-info.outputs.resource_group_name }} \
              --provider application || true

            echo "Checking Key Vault access from app service..."
            az webapp config appsettings list \
              --name ${{ needs.get-infrastructure-info.outputs.backend_app_name }} \
              --resource-group ${{ needs.get-infrastructure-info.outputs.resource_group_name }} \
              --query "[?contains(name, 'OPENAI') || contains(name, 'GPT')].{Name:name, Value:value}" \
              --output table || true

            echo "Checking container logs for startup errors..."

            # Get container logs directly
            echo "=== Container Logs ==="
            az webapp log tail \
              --name ${{ needs.get-infrastructure-info.outputs.backend_app_name }} \
              --resource-group ${{ needs.get-infrastructure-info.outputs.resource_group_name }} \
              --provider application || true

            echo "=== Docker Container Status ==="
            az webapp show \
              --name ${{ needs.get-infrastructure-info.outputs.backend_app_name }} \
              --resource-group ${{ needs.get-infrastructure-info.outputs.resource_group_name }} \
              --query "{State:state, AvailabilityState:availabilityState, UsageState:usageState}" \
              --output table || true

            exit 1
          fi

          echo "Waiting 30 seconds before next attempt..."
          sleep 30
        done

  deploy-frontend:
    name: 🚀 Deploy Frontend
    runs-on: ubuntu-latest
    needs: [determine-environment, get-infrastructure-info, build-and-test]
    if: needs.determine-environment.outputs.should_deploy == 'true' && needs.determine-environment.outputs.deploy_frontend == 'true'
    environment: ${{ needs.determine-environment.outputs.environment }}
    
    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4
    
    - name: 🔐 Azure Login
      uses: azure/login@v2
      with:
        creds: ${{ secrets.AZURE_CREDENTIALS }}
        enable-AzPSSession: true
        environment: azurecloud
        allow-no-subscriptions: false
        audience: api://AzureADTokenExchange
        auth-type: SERVICE_PRINCIPAL

    - name: 🔑 Login to Azure Container Registry
      run: az acr login --name ${{ needs.get-infrastructure-info.outputs.acr_name }}
    
    - name: 🏗️ Build Frontend Image
      working-directory: apps/filtro-curricular/filtro-curricular-fe-web
      run: |
        IMAGE_TAG="${{ github.sha }}"
        ACR_SERVER="${{ needs.get-infrastructure-info.outputs.acr_login_server }}"
        
        docker build \
          --tag "$ACR_SERVER/filtro-curricular-fe-web:$IMAGE_TAG" \
          --tag "$ACR_SERVER/filtro-curricular-fe-web:latest" \
          --tag "$ACR_SERVER/filtro-curricular-fe-web:${{ needs.determine-environment.outputs.environment }}-latest" \
          .
    
    - name: 📤 Push Frontend Image
      run: |
        ACR_SERVER="${{ needs.get-infrastructure-info.outputs.acr_login_server }}"
        IMAGE_TAG="${{ github.sha }}"
        
        docker push "$ACR_SERVER/filtro-curricular-fe-web:$IMAGE_TAG"
        docker push "$ACR_SERVER/filtro-curricular-fe-web:latest"
        docker push "$ACR_SERVER/filtro-curricular-fe-web:${{ needs.determine-environment.outputs.environment }}-latest"
    
    - name: 🔄 Restart Frontend App Service
      run: |
        az webapp restart \
          --name ${{ needs.get-infrastructure-info.outputs.frontend_app_name }} \
          --resource-group ${{ needs.get-infrastructure-info.outputs.resource_group_name }}
    
    - name: ⏳ Wait for Frontend Deployment
      run: |
        echo "Waiting for frontend deployment to complete..."
        sleep 30
        
        # Check if the app is responding
        FRONTEND_URL="https://${{ needs.get-infrastructure-info.outputs.frontend_app_name }}.azurewebsites.net"
        for i in {1..10}; do
          if curl -f "$FRONTEND_URL" > /dev/null 2>&1; then
            echo "Frontend is responding"
            break
          fi
          echo "Attempt $i: Frontend not ready, waiting..."
          sleep 30
        done

  notify-success:
    name: 📧 Notify Success
    runs-on: ubuntu-latest
    needs: [determine-environment, check-infrastructure-workflow, wait-for-infrastructure, get-infrastructure-info, deploy-backend, deploy-frontend]
    if: always() && (needs.deploy-backend.result == 'success' || needs.deploy-frontend.result == 'success')
    
    steps:
    - name: 📊 Deployment Summary
      run: |
        echo "=== 🚀 Filtro Curricular Application Deployment Summary ==="
        echo "Environment: ${{ needs.determine-environment.outputs.environment }}"
        echo "Backend Deployed: ${{ needs.determine-environment.outputs.deploy_backend }}"
        echo "Frontend Deployed: ${{ needs.determine-environment.outputs.deploy_frontend }}"
        echo ""
        echo "=== 🏗️ Infrastructure Coordination ==="
        echo "Infrastructure was running: ${{ needs.check-infrastructure-workflow.outputs.infrastructure_running }}"
        if [ "${{ needs.check-infrastructure-workflow.outputs.should_wait }}" = "true" ]; then
          echo "✅ Waited for infrastructure deployment to complete"
          echo "🔗 Infrastructure Run: https://github.com/${{ github.repository }}/actions/runs/${{ needs.check-infrastructure-workflow.outputs.infrastructure_run_id }}"
        else
          echo "✅ No infrastructure coordination required"
        fi
        echo ""
        echo "=== 🌐 Application URLs ==="
        if [ "${{ needs.deploy-backend.result }}" = "success" ]; then
          echo "Backend: https://${{ needs.get-infrastructure-info.outputs.backend_app_name }}.azurewebsites.net"
        fi
        if [ "${{ needs.deploy-frontend.result }}" = "success" ]; then
          echo "Frontend: https://${{ needs.get-infrastructure-info.outputs.frontend_app_name }}.azurewebsites.net"
        fi

    - name: Placeholder Step
      run: echo "Notifications are currently disabled. Configure SMTP and Teams webhook secrets to enable notifications."

    # TODO: Re-enable email notifications once SMTP secrets are configured
    # - name: Send Email Notification
    #   if: vars.NOTIFICATION_EMAIL
    #   uses: dawidd6/action-send-mail@v3
    #   with:
    #     server_address: ${{ vars.SMTP_SERVER || 'smtp.office365.com' }}
    #     server_port: ${{ vars.SMTP_PORT || '587' }}
    #     username: ${{ secrets.SMTP_USERNAME }}
    #     password: ${{ secrets.SMTP_PASSWORD }}
    #     subject: "✅ Filtro Curricular Applications Deployed - ${{ needs.determine-environment.outputs.environment }}"
    #     to: ${{ vars.NOTIFICATION_EMAIL }}
    #     from: ${{ secrets.SMTP_USERNAME }}
    #     body: |
    #       Application deployment completed successfully!
    #
    #       Environment: ${{ needs.determine-environment.outputs.environment }}
    #       Backend Deployed: ${{ needs.determine-environment.outputs.deploy_backend }}
    #       Frontend Deployed: ${{ needs.determine-environment.outputs.deploy_frontend }}
    #
    #       Backend URL: https://${{ needs.get-infrastructure-info.outputs.backend_app_name }}.azurewebsites.net
    #       Frontend URL: https://${{ needs.get-infrastructure-info.outputs.frontend_app_name }}.azurewebsites.net
    #
    #       Commit: ${{ github.sha }}
    #       Branch: ${{ github.ref_name }}
    #       Actor: ${{ github.actor }}

    # TODO: Re-enable Teams notifications once webhook URL is configured
    # - name: 📢 Send Teams Notification
    #   if: secrets.TEAMS_WEBHOOK_URL
    #   uses: skitionek/notify-microsoft-teams@master
    #   with:
    #     webhook_url: ${{ secrets.TEAMS_WEBHOOK_URL }}
    #     title: "✅ Application Deployment Success"
    #     message: |
    #       **Filtro Curricular Applications Deployed**
    #
    #       **Environment:** ${{ needs.determine-environment.outputs.environment }}
    #       **Backend Deployed:** ${{ needs.determine-environment.outputs.deploy_backend }}
    #       **Frontend Deployed:** ${{ needs.determine-environment.outputs.deploy_frontend }}
    #
    #       **Backend URL:** https://${{ needs.get-infrastructure-info.outputs.backend_app_name }}.azurewebsites.net
    #       **Frontend URL:** https://${{ needs.get-infrastructure-info.outputs.frontend_app_name }}.azurewebsites.net
    #
    #       **Commit:** ${{ github.sha }}
    #       **Branch:** ${{ github.ref_name }}
    #       **Actor:** ${{ github.actor }}

  notify-failure:
    name: 🚨 Notify Failure
    runs-on: ubuntu-latest
    needs: [determine-environment, check-infrastructure-workflow, wait-for-infrastructure, get-infrastructure-info, deploy-backend, deploy-frontend]
    if: failure()
    
    steps:
    - name: 🚨 Failure Summary
      run: |
        echo "=== ❌ Filtro Curricular Application Deployment Failed ==="
        echo "Environment: ${{ needs.determine-environment.outputs.environment }}"
        echo ""
        echo "=== 🏗️ Infrastructure Coordination Status ==="
        echo "Infrastructure was running: ${{ needs.check-infrastructure-workflow.outputs.infrastructure_running }}"
        echo "Should wait result: ${{ needs.check-infrastructure-workflow.outputs.should_wait }}"
        echo "Wait for infrastructure result: ${{ needs.wait-for-infrastructure.result }}"
        echo "Get infrastructure info result: ${{ needs.get-infrastructure-info.result }}"
        echo ""
        echo "=== 📊 Job Results ==="
        echo "Deploy backend result: ${{ needs.deploy-backend.result }}"
        echo "Deploy frontend result: ${{ needs.deploy-frontend.result }}"
        echo ""
        if [ "${{ needs.check-infrastructure-workflow.outputs.should_wait }}" = "true" ]; then
          echo "🔗 Related Infrastructure Run: https://github.com/${{ github.repository }}/actions/runs/${{ needs.check-infrastructure-workflow.outputs.infrastructure_run_id }}"
        fi
        echo "🔗 Current Application Run: https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}"

    - name: 📝 Placeholder Step
      run: echo "Notifications are currently disabled. Configure SMTP and Teams webhook secrets to enable notifications."

    # TODO: Re-enable email notifications once SMTP secrets are configured
    # - name: 📧 Send Email Notification
    #   if: vars.NOTIFICATION_EMAIL
    #   uses: dawidd6/action-send-mail@v3
    #   with:
    #     server_address: ${{ vars.SMTP_SERVER || 'smtp.office365.com' }}
    #     server_port: ${{ vars.SMTP_PORT || '587' }}
    #     username: ${{ secrets.SMTP_USERNAME }}
    #     password: ${{ secrets.SMTP_PASSWORD }}
    #     subject: "❌ Filtro Curricular Application Deployment Failed - ${{ needs.determine-environment.outputs.environment }}"
    #     to: ${{ vars.NOTIFICATION_EMAIL }}
    #     from: ${{ secrets.SMTP_USERNAME }}
    #     body: |
    #       Application deployment failed!
    #
    #       Environment: ${{ needs.determine-environment.outputs.environment }}
    #
    #       Please check the GitHub Actions logs for details.
    #
    #       Commit: ${{ github.sha }}
    #       Branch: ${{ github.ref_name }}
    #       Actor: ${{ github.actor }}

    # TODO: Re-enable Teams notifications once webhook URL is configured
    # - name: 📢 Send Teams Notification
    #   if: secrets.TEAMS_WEBHOOK_URL
    #   uses: skitionek/notify-microsoft-teams@master
    #   with:
    #     webhook_url: ${{ secrets.TEAMS_WEBHOOK_URL }}
    #     title: "❌ Application Deployment Failed"
    #     message: |
    #       **Filtro Curricular Application Deployment Failed**
    #
    #       **Environment:** ${{ needs.determine-environment.outputs.environment }}
    #
    #       Please check the GitHub Actions logs for details.
    #
    #       **Commit:** ${{ github.sha }}
    #       **Branch:** ${{ github.ref_name }}
    #       **Actor:** ${{ github.actor }}
