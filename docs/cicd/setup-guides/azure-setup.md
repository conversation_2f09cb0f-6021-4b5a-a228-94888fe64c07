# Azure Infrastructure Setup Guide

## 🎯 Overview

This guide walks you through setting up the complete Azure infrastructure for the Filtro Curricular application, including secrets management with Azure Key Vault.

## 📋 Prerequisites

### Required Tools
```bash
# Install Azure CLI
curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash

# Install Terraform
wget https://releases.hashicorp.com/terraform/1.5.0/terraform_1.5.0_linux_amd64.zip
unzip terraform_1.5.0_linux_amd64.zip
sudo mv terraform /usr/local/bin/

# Install GitHub CLI
curl -fsSL https://cli.github.com/packages/githubcli-archive-keyring.gpg | sudo dd of=/usr/share/keyrings/githubcli-archive-keyring.gpg
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/githubcli-archive-keyring.gpg] https://cli.github.com/packages stable main" | sudo tee /etc/apt/sources.list.d/github-cli.list > /dev/null
sudo apt update && sudo apt install gh
```

### Azure Account Setup
```bash
# Login to Azure
az login

# Set subscription (if you have multiple)
az account set --subscription "your-subscription-id"

# Verify current subscription
az account show --output table
```

## 🔐 Step 1: Create Azure Service Principal

### 1.1 Create Service Principal for GitHub Actions
```bash
# Create service principal with contributor role
az ad sp create-for-rbac \
  --name "sp-ragtech-github-actions" \
  --role contributor \
  --scopes /subscriptions/$(az account show --query id -o tsv) \
  --sdk-auth

# Output will be JSON like:
{
  "clientId": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
  "clientSecret": "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
  "subscriptionId": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
  "tenantId": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
  "activeDirectoryEndpointUrl": "https://login.microsoftonline.com",
  "resourceManagerEndpointUrl": "https://management.azure.com/",
  "activeDirectoryGraphResourceId": "https://graph.windows.net/",
  "sqlManagementEndpointUrl": "https://management.core.windows.net:8443/",
  "galleryEndpointUrl": "https://gallery.azure.com/",
  "managementEndpointUrl": "https://management.core.windows.net/"
}
```

### 1.2 Store Service Principal in GitHub Secrets
```bash
# Store the entire JSON output as AZURE_CREDENTIALS
gh secret set AZURE_CREDENTIALS --body '{
  "clientId": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
  "clientSecret": "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
  "subscriptionId": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
  "tenantId": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
  "activeDirectoryEndpointUrl": "https://login.microsoftonline.com",
  "resourceManagerEndpointUrl": "https://management.azure.com/",
  "activeDirectoryGraphResourceId": "https://graph.windows.net/",
  "sqlManagementEndpointUrl": "https://management.core.windows.net:8443/",
  "galleryEndpointUrl": "https://gallery.azure.com/",
  "managementEndpointUrl": "https://management.core.windows.net/"
}'
```

## 🔑 Step 2: Configure OpenAI Secrets

### 2.1 Obtain OpenAI API Keys
1. Visit [OpenAI Platform](https://platform.openai.com/api-keys)
2. Create new API key for GPT models
3. Create new API key for Assistant API (if different)
4. Create Assistant IDs for specific domains

### 2.2 Store OpenAI Secrets in GitHub
```bash
# Store OpenAI secrets
gh secret set OPENAI_API_KEY --body "sk-proj-your-api-key-here"
gh secret set OPENAI_TOKEN --body "sk-your-token-here"
gh secret set ASSISTANT_ID_JURIDICO --body "asst_your-juridico-assistant-id"
gh secret set ASSISTANT_ID_CALIDAD --body "asst_your-calidad-assistant-id"

# Verify secrets are set
gh secret list
```

## 🏗️ Step 3: Configure Terraform Backend

### 3.1 Create Storage Account for Terraform State
```bash
# Create resource group for Terraform state
az group create --name rg-terraform-state --location brazilsouth

# Create storage account (name must be globally unique)
STORAGE_ACCOUNT_NAME="stterraformstate$(date +%s)"
az storage account create \
  --resource-group rg-terraform-state \
  --name $STORAGE_ACCOUNT_NAME \
  --sku Standard_LRS \
  --encryption-services blob

# Create container for state files
az storage container create \
  --name tfstate \
  --account-name $STORAGE_ACCOUNT_NAME
```

### 3.2 Configure Terraform Backend
```hcl
# Create apps/infrastructure/azure/filtro-curricular/backend.tf
terraform {
  backend "azurerm" {
    resource_group_name  = "rg-terraform-state"
    storage_account_name = "stterraformstateXXXXXXXXXX"  # Replace with your storage account name
    container_name       = "tfstate"
    key                  = "filtro-curricular.tfstate"
  }
}
```

## 🚀 Step 4: Deploy Infrastructure

### 4.1 Initialize Terraform
```bash
cd apps/infrastructure/azure/filtro-curricular

# Initialize Terraform
terraform init

# Validate configuration
terraform validate
```

### 4.2 Configure Variables
```bash
# Copy example variables
cp terraform.tfvars.example terraform.tfvars

# Edit terraform.tfvars with your values
nano terraform.tfvars
```

### 4.3 Deploy via GitHub Actions
```bash
# Trigger infrastructure deployment
gh workflow run filtro-curricular-infrastructure.yml \
  --field environment=dev \
  --field destroy=false

# Monitor deployment
gh run list --workflow=filtro-curricular-infrastructure.yml
```

## 🔧 Step 5: Configure Secrets Management

### 5.1 Verify Key Vault Creation
```bash
# List Key Vaults in resource group
az keyvault list --resource-group rg-filtro-dev --output table

# Get Key Vault name
KEY_VAULT_NAME=$(az keyvault list --resource-group rg-filtro-dev --query "[0].name" -o tsv)
echo "Key Vault: $KEY_VAULT_NAME"
```

### 5.2 Verify Secrets in Key Vault
```bash
# List secrets in Key Vault
az keyvault secret list --vault-name $KEY_VAULT_NAME --output table

# Verify specific secrets exist
az keyvault secret show --vault-name $KEY_VAULT_NAME --name openai-api-key --query "attributes.enabled"
az keyvault secret show --vault-name $KEY_VAULT_NAME --name openai-token --query "attributes.enabled"
```

### 5.3 Test Secret Access
```bash
# Test secret retrieval (be careful - this shows actual secret value)
az keyvault secret show --vault-name $KEY_VAULT_NAME --name openai-api-key --query "value" -o tsv
```

## 📱 Step 6: Deploy Applications

### 6.1 Trigger Application Deployment
```bash
# Deploy both backend and frontend
gh workflow run filtro-curricular-applications.yml \
  --field environment=dev \
  --field component=both \
  --field force_deploy=false

# Monitor deployment
gh run list --workflow=filtro-curricular-applications.yml
```

### 6.2 Verify Application Health
```bash
# Get App Service URLs
BACKEND_URL=$(az webapp show --name filtro-be-dev-* --resource-group rg-filtro-dev --query "defaultHostName" -o tsv)
FRONTEND_URL=$(az webapp show --name filtro-fe-dev-* --resource-group rg-filtro-dev --query "defaultHostName" -o tsv)

# Test backend health
curl https://$BACKEND_URL/health

# Test frontend
curl https://$FRONTEND_URL/
```

## 🔄 Step 7: Secret Rotation Setup

### 7.1 Configure Automatic Secret Rotation
```bash
# Enable Key Vault notifications (optional)
az eventgrid event-subscription create \
  --name secret-rotation-notifications \
  --source-resource-id "/subscriptions/$(az account show --query id -o tsv)/resourceGroups/rg-filtro-dev/providers/Microsoft.KeyVault/vaults/$KEY_VAULT_NAME" \
  --endpoint-type webhook \
  --endpoint "https://your-notification-endpoint.com/webhook"
```

### 7.2 Manual Secret Update Process
```bash
# Update secrets using the management script
cd apps/infrastructure/azure/filtro-curricular
./scripts/manage-secrets.sh update

# Or update from file
./scripts/manage-secrets.sh update-from-file secrets/secrets.json
```

## 📊 Step 8: Monitoring Setup

### 8.1 Configure Application Insights
```bash
# Get Application Insights instrumentation key
APP_INSIGHTS_KEY=$(az monitor app-insights component show \
  --app filtro-ai-dev-* \
  --resource-group rg-filtro-dev \
  --query "instrumentationKey" -o tsv)

echo "Application Insights Key: $APP_INSIGHTS_KEY"
```

### 8.2 Set Up Alerts
```bash
# Create alert for application failures
az monitor metrics alert create \
  --name "High Error Rate" \
  --resource-group rg-filtro-dev \
  --scopes "/subscriptions/$(az account show --query id -o tsv)/resourceGroups/rg-filtro-dev/providers/Microsoft.Web/sites/filtro-be-dev-*" \
  --condition "avg requests/failed > 10" \
  --description "Alert when error rate is high"
```

## 🚨 Troubleshooting

### Common Issues

#### Issue 1: Service Principal Permissions
```bash
# Grant additional permissions if needed
az role assignment create \
  --assignee "your-service-principal-id" \
  --role "Key Vault Administrator" \
  --scope "/subscriptions/$(az account show --query id -o tsv)"
```

#### Issue 2: Key Vault Access Denied
```bash
# Check access policies
az keyvault show --name $KEY_VAULT_NAME --query "properties.accessPolicies"

# Add access policy for your user
az keyvault set-policy \
  --name $KEY_VAULT_NAME \
  --upn <EMAIL> \
  --secret-permissions get list set delete
```

#### Issue 3: Terraform State Lock
```bash
# Force unlock if needed (use with caution)
terraform force-unlock LOCK_ID
```

## ✅ Verification Checklist

- [ ] Azure CLI installed and authenticated
- [ ] Service Principal created and stored in GitHub Secrets
- [ ] OpenAI secrets configured in GitHub
- [ ] Terraform backend configured
- [ ] Infrastructure deployed successfully
- [ ] Key Vault created with secrets
- [ ] Applications deployed and healthy
- [ ] Secret access working correctly
- [ ] Monitoring configured

## 📚 Next Steps

1. **Set up additional environments**: Repeat process for staging/prod
2. **Configure monitoring**: Set up comprehensive monitoring and alerting
3. **Implement secret rotation**: Automate secret rotation procedures
4. **Security review**: Conduct security assessment of the setup
5. **Documentation**: Update team documentation with environment-specific details

---

**Support**: For issues with this setup, check the [troubleshooting guide](../troubleshooting/common-issues.md) or contact the DevOps team.
