# GitHub Actions Workflow Coordination

## 📋 Overview

The Filtro Curricular project uses two main GitHub Actions workflows that need to be coordinated:

1. **Infrastructure Workflow** (`filtro-curricular-infrastructure.yml`) - Deploys Azure infrastructure using Terraform
2. **Applications Workflow** (`filtro-curricular-applications.yml`) - Builds and deploys containerized applications

## 🔄 Coordination Logic

### Problem
When both infrastructure and application code changes are pushed simultaneously, both workflows trigger. The application deployment may fail if it tries to deploy before the infrastructure is ready.

### Solution
The applications workflow now automatically detects and waits for any running infrastructure workflow to complete before proceeding.

## 🏗️ How It Works

### 1. Infrastructure Detection
```yaml
check-infrastructure-workflow:
  - Checks for running infrastructure workflows on the same commit/branch
  - Uses GitHub API to query workflow status
  - Sets outputs indicating if waiting is required
```

### 2. Conditional Waiting
```yaml
wait-for-infrastructure:
  - Only runs if infrastructure workflow is detected
  - Polls infrastructure workflow status every 30 seconds
  - Waits up to 1 hour for completion
  - Fails if infrastructure deployment fails
```

### 3. Coordinated Deployment
```yaml
get-infrastructure-info:
  - Depends on both detection and waiting jobs
  - Only proceeds when infrastructure is ready
  - Continues normal application deployment flow
```

### Example Workflow Flow
```mermaid
graph TD
    A[Push to Repository] --> B{Both Workflows Triggered?}
    B -->|Yes| C[Infrastructure Workflow Starts]
    B -->|No| D[Single Workflow Runs Normally]
    C --> E[Applications Workflow Detects Infrastructure]
    E --> F[Applications Workflow Waits]
    F --> G{Infrastructure Success?}
    G -->|Yes| H[Applications Deployment Proceeds]
    G -->|No| I[Applications Deployment Fails]
    H --> J[Both Deployments Complete]
    style A fill:#e1f5fe
    style J fill:#c8e6c9
```

## 🎯 Trigger Scenarios

### Scenario 1: Infrastructure Changes Only
- Infrastructure workflow runs
- Applications workflow doesn't trigger
- **Result**: Infrastructure deploys normally

### Scenario 2: Application Changes Only
- Applications workflow runs
- No infrastructure workflow detected
- **Result**: Applications deploy immediately

### Scenario 3: Both Infrastructure and Application Changes
- Both workflows trigger simultaneously
- Applications workflow detects running infrastructure workflow
- Applications workflow waits for infrastructure completion
- **Result**: Infrastructure deploys first, then applications

### Scenario 4: Manual Deployments
- Manual infrastructure deployment via workflow_dispatch
- Subsequent application deployment automatically waits
- **Result**: Proper coordination maintained

## 📊 Monitoring

### Workflow Logs
The coordination process provides detailed logging:

```
🔍 Checking for running infrastructure workflows...
🏗️ Infrastructure workflow is running (ID: 12345). Application deployment will wait.
⏳ Infrastructure workflow still running. Waiting 30 seconds...
✅ Infrastructure workflow completed successfully!
```

### GitHub UI
- Infrastructure workflow run ID is displayed in logs
- Direct links to related workflows are provided
- Deployment summary shows coordination status

## ⚙️ Configuration

### Coordination Methods

The system supports two coordination methods:

#### 1. API-Based Coordination (Recommended)
- **Requires**: Personal Access Token (PAT) with `actions:read` scope
- **Benefits**: Real-time workflow status monitoring
- **Accuracy**: High - knows exact workflow status

#### 2. File-Based Coordination (Fallback)
- **Requires**: No additional setup
- **Benefits**: Works with default GitHub token
- **Accuracy**: Medium - uses git history and time-based delays

### Setting Up API-Based Coordination

1. **Create a Personal Access Token**:
   - Go to GitHub Settings → Developer settings → Personal access tokens
   - Create a new token with `actions:read` scope
   - Copy the token value

2. **Add Token to Repository Secrets**:
   - Go to Repository Settings → Secrets and variables → Actions
   - Add new secret named `WORKFLOW_COORDINATION_TOKEN`
   - Paste the PAT value

3. **Update Workflow** (if using custom token):
   ```yaml
   env:
     GITHUB_TOKEN: ${{ secrets.WORKFLOW_COORDINATION_TOKEN || secrets.GITHUB_TOKEN }}
   ```

### Timeouts
- **API-based maximum wait time**: 1 hour (3600 seconds)
- **API-based check interval**: 30 seconds
- **File-based wait time**: 5 minutes (300 seconds)
- **All timeouts configurable** in workflow file

### Error Handling
- **API method**: Infrastructure failure → Application deployment fails
- **File method**: Always proceeds after timeout (graceful degradation)
- **API unavailable**: Falls back to file-based method automatically

## 🚨 Troubleshooting

### Common Issues

1. **403 "Resource not accessible by integration" Error**
   - **Cause**: Default GITHUB_TOKEN lacks `actions:read` permission
   - **Solution**: Set up PAT with `actions:read` scope (see Configuration section)
   - **Workaround**: System automatically falls back to file-based coordination

2. **"not: command not found" Shell Error**
   - **Cause**: JSON error response being treated as shell command
   - **Solution**: Fixed with proper error handling in updated workflow
   - **Prevention**: Always validate API responses before using

3. **Long Wait Times**
   - Check infrastructure workflow for issues
   - Monitor Azure resource provisioning
   - Consider infrastructure complexity
   - API method: Real-time monitoring
   - File method: Fixed 5-minute wait

4. **Timeout Errors**
   - API method: Increase `MAX_WAIT_TIME` if needed
   - File method: Adjust wait duration
   - Check for stuck infrastructure deployments
   - Verify Azure service availability

5. **False Positives**
   - API method: Uses exact commit SHA and branch matching
   - File method: May trigger on any infrastructure file changes
   - Ensure proper branch naming
   - Check for concurrent deployments

### Manual Override
If coordination fails, you can:
1. Cancel the stuck infrastructure workflow
2. Re-run the application workflow
3. Use manual deployment with specific environment

## 🔧 Customization

### Adjusting Wait Time
```yaml
MAX_WAIT_TIME=7200  # 2 hours
WAIT_INTERVAL=60    # Check every minute
```

### Adding Notifications
The coordination system integrates with existing notification jobs and can be extended to send alerts during long waits.

## 📈 Benefits

1. **Reliability**: Prevents application deployment failures due to missing infrastructure
2. **Automation**: No manual intervention required for coordination
3. **Visibility**: Clear logging and status reporting
4. **Flexibility**: Works with both automatic and manual deployments
5. **Safety**: Fails fast if infrastructure deployment fails

## 🔗 Related Documentation

- [GitHub Actions Setup Guide](../infrastructure/azure/filtro-curricular/docs/GITHUB_ACTIONS_SETUP.md)
- [Deployment Workflows](./workflows/filtro-curricular-azure.md)
- [Infrastructure Documentation](../infrastructure/azure/filtro-curricular/docs/README.md)
